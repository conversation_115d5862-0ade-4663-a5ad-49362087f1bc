<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>bundleid</key>
	<string>com.clintonstrong.SearchTabs</string>
	<key>category</key>
	<string>Internet</string>
	<key>connections</key>
	<dict>
		<key>0E88E3D0-4E7B-48B2-88ED-5CF0EBD76F5D</key>
		<array>
			<dict>
				<key>destinationuid</key>
				<string>73C183A0-4D38-4DA7-A383-F36A5C7CBB95</string>
				<key>modifiers</key>
				<integer>0</integer>
				<key>modifiersubtext</key>
				<string></string>
				<key>vitoclose</key>
				<false/>
			</dict>
			<dict>
				<key>destinationuid</key>
				<string>1F537C5E-7F6F-4B71-987D-DAF5775545CF</string>
				<key>modifiers</key>
				<integer>262144</integer>
				<key>modifiersubtext</key>
				<string>Close this tab</string>
				<key>vitoclose</key>
				<false/>
			</dict>
			<dict>
				<key>destinationuid</key>
				<string>CAE17E6D-9008-4467-9FD6-39A943E6AC54</string>
				<key>modifiers</key>
				<integer>1048576</integer>
				<key>modifiersubtext</key>
				<string>Copy URL</string>
				<key>vitoclose</key>
				<false/>
			</dict>
		</array>
		<key>1F537C5E-7F6F-4B71-987D-DAF5775545CF</key>
		<array>
			<dict>
				<key>destinationuid</key>
				<string>7F6B33E3-0538-4263-85FC-DD755949B570</string>
				<key>modifiers</key>
				<integer>0</integer>
				<key>modifiersubtext</key>
				<string></string>
				<key>vitoclose</key>
				<false/>
			</dict>
		</array>
		<key>22F259A2-1FA0-4E8C-A5AC-39F6384FD8BD</key>
		<array>
			<dict>
				<key>destinationuid</key>
				<string>0E88E3D0-4E7B-48B2-88ED-5CF0EBD76F5D</string>
				<key>modifiers</key>
				<integer>0</integer>
				<key>modifiersubtext</key>
				<string></string>
				<key>vitoclose</key>
				<false/>
			</dict>
		</array>
		<key>29A3619D-A2A2-47EE-8851-4E11EDC74781</key>
		<array>
			<dict>
				<key>destinationuid</key>
				<string>FB48433C-B8B1-4972-8BAB-FF5B28029584</string>
				<key>modifiers</key>
				<integer>0</integer>
				<key>modifiersubtext</key>
				<string></string>
				<key>vitoclose</key>
				<false/>
			</dict>
			<dict>
				<key>destinationuid</key>
				<string>7466BB6C-FAC1-4334-837E-3B9BA9DED68D</string>
				<key>modifiers</key>
				<integer>262144</integer>
				<key>modifiersubtext</key>
				<string>Close this tab</string>
				<key>vitoclose</key>
				<false/>
			</dict>
			<dict>
				<key>destinationuid</key>
				<string>DB0D0954-88CC-4CFF-BD37-C84FA2EE4B6F</string>
				<key>modifiers</key>
				<integer>1048576</integer>
				<key>modifiersubtext</key>
				<string>Copy URL</string>
				<key>vitoclose</key>
				<false/>
			</dict>
		</array>
		<key>2D2E8068-5A71-44AE-84D6-AFF8FD1B307A</key>
		<array>
			<dict>
				<key>destinationuid</key>
				<string>0E88E3D0-4E7B-48B2-88ED-5CF0EBD76F5D</string>
				<key>modifiers</key>
				<integer>0</integer>
				<key>modifiersubtext</key>
				<string></string>
				<key>vitoclose</key>
				<false/>
			</dict>
		</array>
		<key>32220A19-5EE0-4385-AB98-FB3C983430F6</key>
		<array>
			<dict>
				<key>destinationuid</key>
				<string>2D2E8068-5A71-44AE-84D6-AFF8FD1B307A</string>
				<key>modifiers</key>
				<integer>0</integer>
				<key>modifiersubtext</key>
				<string></string>
				<key>vitoclose</key>
				<false/>
			</dict>
		</array>
		<key>4F271C23-8A51-4853-99C4-4096C226A358</key>
		<array>
			<dict>
				<key>destinationuid</key>
				<string>29A3619D-A2A2-47EE-8851-4E11EDC74781</string>
				<key>modifiers</key>
				<integer>0</integer>
				<key>modifiersubtext</key>
				<string></string>
				<key>vitoclose</key>
				<false/>
			</dict>
		</array>
		<key>7466BB6C-FAC1-4334-837E-3B9BA9DED68D</key>
		<array>
			<dict>
				<key>destinationuid</key>
				<string>B23BD300-027B-4347-B23E-2DAE51EFCCD8</string>
				<key>modifiers</key>
				<integer>0</integer>
				<key>modifiersubtext</key>
				<string></string>
				<key>vitoclose</key>
				<false/>
			</dict>
		</array>
		<key>774028EF-CE92-4D2B-BAA5-4FFD9DE00FF8</key>
		<array>
			<dict>
				<key>destinationuid</key>
				<string>4F271C23-8A51-4853-99C4-4096C226A358</string>
				<key>modifiers</key>
				<integer>0</integer>
				<key>modifiersubtext</key>
				<string></string>
				<key>vitoclose</key>
				<false/>
			</dict>
		</array>
		<key>7783D7DE-E932-4ACB-B890-4A7EAFF473C2</key>
		<array>
			<dict>
				<key>destinationuid</key>
				<string>29A3619D-A2A2-47EE-8851-4E11EDC74781</string>
				<key>modifiers</key>
				<integer>0</integer>
				<key>modifiersubtext</key>
				<string></string>
				<key>vitoclose</key>
				<false/>
			</dict>
		</array>
		<key>92C1DBD0-E4BB-48D7-99B7-D2C977C9369A</key>
		<array>
			<dict>
				<key>destinationuid</key>
				<string>29A3619D-A2A2-47EE-8851-4E11EDC74781</string>
				<key>modifiers</key>
				<integer>0</integer>
				<key>modifiersubtext</key>
				<string></string>
				<key>vitoclose</key>
				<false/>
			</dict>
		</array>
		<key>FB48433C-B8B1-4972-8BAB-FF5B28029584</key>
		<array/>
	</dict>
	<key>createdby</key>
	<string>Clinton Strong</string>
	<key>description</key>
	<string></string>
	<key>disabled</key>
	<false/>
	<key>name</key>
	<string>Search Safari and Chrome Tabs</string>
	<key>objects</key>
	<array>
		<dict>
			<key>config</key>
			<dict>
				<key>alfredfiltersresults</key>
				<false/>
				<key>alfredfiltersresultsmatchmode</key>
				<integer>0</integer>
				<key>argumenttreatemptyqueryasnil</key>
				<false/>
				<key>argumenttrimmode</key>
				<integer>0</integer>
				<key>argumenttype</key>
				<integer>1</integer>
				<key>escaping</key>
				<integer>102</integer>
				<key>keyword</key>
				<string>pages</string>
				<key>queuedelaycustom</key>
				<integer>1</integer>
				<key>queuedelayimmediatelyinitially</key>
				<false/>
				<key>queuedelaymode</key>
				<integer>0</integer>
				<key>queuemode</key>
				<integer>1</integer>
				<key>runningsubtext</key>
				<string>Reading data...</string>
				<key>script</key>
				<string>/usr/bin/ruby filter.rb "{query}"
#./filter "{query}"</string>
				<key>scriptargtype</key>
				<integer>0</integer>
				<key>scriptfile</key>
				<string></string>
				<key>subtext</key>
				<string></string>
				<key>title</key>
				<string>Search tabs and browser history ...</string>
				<key>type</key>
				<integer>0</integer>
				<key>withspace</key>
				<false/>
			</dict>
			<key>type</key>
			<string>alfred.workflow.input.scriptfilter</string>
			<key>uid</key>
			<string>0E88E3D0-4E7B-48B2-88ED-5CF0EBD76F5D</string>
			<key>version</key>
			<integer>3</integer>
		</dict>
		<dict>
			<key>config</key>
			<dict>
				<key>concurrently</key>
				<false/>
				<key>escaping</key>
				<integer>102</integer>
				<key>script</key>
				<string>ruby actions/activate.rb "{query}"</string>
				<key>scriptargtype</key>
				<integer>0</integer>
				<key>scriptfile</key>
				<string></string>
				<key>type</key>
				<integer>0</integer>
			</dict>
			<key>type</key>
			<string>alfred.workflow.action.script</string>
			<key>uid</key>
			<string>73C183A0-4D38-4DA7-A383-F36A5C7CBB95</string>
			<key>version</key>
			<integer>2</integer>
		</dict>
		<dict>
			<key>config</key>
			<dict>
				<key>action</key>
				<integer>0</integer>
				<key>argument</key>
				<integer>0</integer>
				<key>focusedappvariable</key>
				<false/>
				<key>focusedappvariablename</key>
				<string></string>
				<key>hotkey</key>
				<integer>49</integer>
				<key>hotmod</key>
				<integer>1835008</integer>
				<key>hotstring</key>
				<string>Space</string>
				<key>leftcursor</key>
				<false/>
				<key>modsmode</key>
				<integer>2</integer>
				<key>relatedAppsMode</key>
				<integer>0</integer>
			</dict>
			<key>type</key>
			<string>alfred.workflow.trigger.hotkey</string>
			<key>uid</key>
			<string>22F259A2-1FA0-4E8C-A5AC-39F6384FD8BD</string>
			<key>version</key>
			<integer>2</integer>
		</dict>
		<dict>
			<key>config</key>
			<dict>
				<key>concurrently</key>
				<false/>
				<key>escaping</key>
				<integer>102</integer>
				<key>script</key>
				<string>ruby actions/close.rb "{query}"</string>
				<key>scriptargtype</key>
				<integer>0</integer>
				<key>scriptfile</key>
				<string></string>
				<key>type</key>
				<integer>0</integer>
			</dict>
			<key>type</key>
			<string>alfred.workflow.action.script</string>
			<key>uid</key>
			<string>1F537C5E-7F6F-4B71-987D-DAF5775545CF</string>
			<key>version</key>
			<integer>2</integer>
		</dict>
		<dict>
			<key>config</key>
			<dict>
				<key>externaltriggerid</key>
				<string>pages</string>
				<key>passinputasargument</key>
				<true/>
				<key>passvariables</key>
				<true/>
				<key>workflowbundleid</key>
				<string>self</string>
			</dict>
			<key>type</key>
			<string>alfred.workflow.output.callexternaltrigger</string>
			<key>uid</key>
			<string>7F6B33E3-0538-4263-85FC-DD755949B570</string>
			<key>version</key>
			<integer>1</integer>
		</dict>
		<dict>
			<key>config</key>
			<dict>
				<key>availableviaurlhandler</key>
				<false/>
				<key>triggerid</key>
				<string>pages</string>
			</dict>
			<key>type</key>
			<string>alfred.workflow.trigger.external</string>
			<key>uid</key>
			<string>32220A19-5EE0-4385-AB98-FB3C983430F6</string>
			<key>version</key>
			<integer>1</integer>
		</dict>
		<dict>
			<key>config</key>
			<dict>
				<key>argument</key>
				<string>{var:query}</string>
				<key>passthroughargument</key>
				<false/>
				<key>variables</key>
				<dict/>
			</dict>
			<key>type</key>
			<string>alfred.workflow.utility.argument</string>
			<key>uid</key>
			<string>2D2E8068-5A71-44AE-84D6-AFF8FD1B307A</string>
			<key>version</key>
			<integer>1</integer>
		</dict>
		<dict>
			<key>config</key>
			<dict>
				<key>autopaste</key>
				<false/>
				<key>clipboardtext</key>
				<string>{query}</string>
				<key>ignoredynamicplaceholders</key>
				<false/>
				<key>transient</key>
				<false/>
			</dict>
			<key>type</key>
			<string>alfred.workflow.output.clipboard</string>
			<key>uid</key>
			<string>CAE17E6D-9008-4467-9FD6-39A943E6AC54</string>
			<key>version</key>
			<integer>3</integer>
		</dict>
		<dict>
			<key>config</key>
			<dict>
				<key>action</key>
				<integer>0</integer>
				<key>argument</key>
				<integer>0</integer>
				<key>focusedappvariable</key>
				<false/>
				<key>focusedappvariablename</key>
				<string></string>
				<key>hotkey</key>
				<integer>49</integer>
				<key>hotmod</key>
				<integer>1572864</integer>
				<key>hotstring</key>
				<string>Space</string>
				<key>leftcursor</key>
				<false/>
				<key>modsmode</key>
				<integer>2</integer>
				<key>relatedAppsMode</key>
				<integer>0</integer>
			</dict>
			<key>type</key>
			<string>alfred.workflow.trigger.hotkey</string>
			<key>uid</key>
			<string>7783D7DE-E932-4ACB-B890-4A7EAFF473C2</string>
			<key>version</key>
			<integer>2</integer>
		</dict>
		<dict>
			<key>config</key>
			<dict>
				<key>concurrently</key>
				<false/>
				<key>escaping</key>
				<integer>102</integer>
				<key>script</key>
				<string>ruby actions/activate.rb "{query}"</string>
				<key>scriptargtype</key>
				<integer>0</integer>
				<key>scriptfile</key>
				<string></string>
				<key>type</key>
				<integer>0</integer>
			</dict>
			<key>type</key>
			<string>alfred.workflow.action.script</string>
			<key>uid</key>
			<string>FB48433C-B8B1-4972-8BAB-FF5B28029584</string>
			<key>version</key>
			<integer>2</integer>
		</dict>
		<dict>
			<key>config</key>
			<dict>
				<key>action</key>
				<integer>0</integer>
				<key>argument</key>
				<integer>0</integer>
				<key>focusedappvariable</key>
				<false/>
				<key>focusedappvariablename</key>
				<string></string>
				<key>hotkey</key>
				<integer>0</integer>
				<key>hotmod</key>
				<integer>0</integer>
				<key>hotstring</key>
				<string></string>
				<key>leftcursor</key>
				<false/>
				<key>modsmode</key>
				<integer>2</integer>
				<key>relatedAppsMode</key>
				<integer>0</integer>
			</dict>
			<key>type</key>
			<string>alfred.workflow.trigger.hotkey</string>
			<key>uid</key>
			<string>92C1DBD0-E4BB-48D7-99B7-D2C977C9369A</string>
			<key>version</key>
			<integer>2</integer>
		</dict>
		<dict>
			<key>config</key>
			<dict>
				<key>alfredfiltersresults</key>
				<false/>
				<key>alfredfiltersresultsmatchmode</key>
				<integer>0</integer>
				<key>argumenttreatemptyqueryasnil</key>
				<false/>
				<key>argumenttrimmode</key>
				<integer>0</integer>
				<key>argumenttype</key>
				<integer>1</integer>
				<key>escaping</key>
				<integer>102</integer>
				<key>keyword</key>
				<string>tabs</string>
				<key>queuedelaycustom</key>
				<integer>1</integer>
				<key>queuedelayimmediatelyinitially</key>
				<false/>
				<key>queuedelaymode</key>
				<integer>0</integer>
				<key>queuemode</key>
				<integer>1</integer>
				<key>runningsubtext</key>
				<string>Reading data...</string>
				<key>script</key>
				<string>ruby filter_tabs.rb "{query}"</string>
				<key>scriptargtype</key>
				<integer>0</integer>
				<key>scriptfile</key>
				<string></string>
				<key>subtext</key>
				<string></string>
				<key>title</key>
				<string>Search tabs ...</string>
				<key>type</key>
				<integer>0</integer>
				<key>withspace</key>
				<false/>
			</dict>
			<key>type</key>
			<string>alfred.workflow.input.scriptfilter</string>
			<key>uid</key>
			<string>29A3619D-A2A2-47EE-8851-4E11EDC74781</string>
			<key>version</key>
			<integer>3</integer>
		</dict>
		<dict>
			<key>config</key>
			<dict>
				<key>availableviaurlhandler</key>
				<false/>
				<key>triggerid</key>
				<string>tabs</string>
			</dict>
			<key>type</key>
			<string>alfred.workflow.trigger.external</string>
			<key>uid</key>
			<string>774028EF-CE92-4D2B-BAA5-4FFD9DE00FF8</string>
			<key>version</key>
			<integer>1</integer>
		</dict>
		<dict>
			<key>config</key>
			<dict>
				<key>concurrently</key>
				<false/>
				<key>escaping</key>
				<integer>102</integer>
				<key>script</key>
				<string>ruby actions/close.rb "{query}"</string>
				<key>scriptargtype</key>
				<integer>0</integer>
				<key>scriptfile</key>
				<string></string>
				<key>type</key>
				<integer>0</integer>
			</dict>
			<key>type</key>
			<string>alfred.workflow.action.script</string>
			<key>uid</key>
			<string>7466BB6C-FAC1-4334-837E-3B9BA9DED68D</string>
			<key>version</key>
			<integer>2</integer>
		</dict>
		<dict>
			<key>config</key>
			<dict>
				<key>externaltriggerid</key>
				<string>tabs</string>
				<key>passinputasargument</key>
				<true/>
				<key>passvariables</key>
				<true/>
				<key>workflowbundleid</key>
				<string>self</string>
			</dict>
			<key>type</key>
			<string>alfred.workflow.output.callexternaltrigger</string>
			<key>uid</key>
			<string>B23BD300-027B-4347-B23E-2DAE51EFCCD8</string>
			<key>version</key>
			<integer>1</integer>
		</dict>
		<dict>
			<key>config</key>
			<dict>
				<key>argument</key>
				<string>{var:query}</string>
				<key>passthroughargument</key>
				<false/>
				<key>variables</key>
				<dict/>
			</dict>
			<key>type</key>
			<string>alfred.workflow.utility.argument</string>
			<key>uid</key>
			<string>4F271C23-8A51-4853-99C4-4096C226A358</string>
			<key>version</key>
			<integer>1</integer>
		</dict>
		<dict>
			<key>config</key>
			<dict>
				<key>autopaste</key>
				<false/>
				<key>clipboardtext</key>
				<string>{query}</string>
				<key>ignoredynamicplaceholders</key>
				<false/>
				<key>transient</key>
				<false/>
			</dict>
			<key>type</key>
			<string>alfred.workflow.output.clipboard</string>
			<key>uid</key>
			<string>DB0D0954-88CC-4CFF-BD37-C84FA2EE4B6F</string>
			<key>version</key>
			<integer>3</integer>
		</dict>
	</array>
	<key>readme</key>
	<string></string>
	<key>uidata</key>
	<dict>
		<key>0E88E3D0-4E7B-48B2-88ED-5CF0EBD76F5D</key>
		<dict>
			<key>xpos</key>
			<real>340</real>
			<key>ypos</key>
			<real>50</real>
		</dict>
		<key>1F537C5E-7F6F-4B71-987D-DAF5775545CF</key>
		<dict>
			<key>xpos</key>
			<real>630</real>
			<key>ypos</key>
			<real>170</real>
		</dict>
		<key>22F259A2-1FA0-4E8C-A5AC-39F6384FD8BD</key>
		<dict>
			<key>xpos</key>
			<real>70</real>
			<key>ypos</key>
			<real>50</real>
		</dict>
		<key>29A3619D-A2A2-47EE-8851-4E11EDC74781</key>
		<dict>
			<key>note</key>
			<string>Filter tabs</string>
			<key>xpos</key>
			<real>340</real>
			<key>ypos</key>
			<real>680</real>
		</dict>
		<key>2D2E8068-5A71-44AE-84D6-AFF8FD1B307A</key>
		<dict>
			<key>note</key>
			<string>Use var:query as argument</string>
			<key>xpos</key>
			<real>240</real>
			<key>ypos</key>
			<real>210</real>
		</dict>
		<key>32220A19-5EE0-4385-AB98-FB3C983430F6</key>
		<dict>
			<key>xpos</key>
			<real>70</real>
			<key>ypos</key>
			<real>180</real>
		</dict>
		<key>4F271C23-8A51-4853-99C4-4096C226A358</key>
		<dict>
			<key>note</key>
			<string>Use var:query as argument</string>
			<key>xpos</key>
			<real>240</real>
			<key>ypos</key>
			<real>840</real>
		</dict>
		<key>73C183A0-4D38-4DA7-A383-F36A5C7CBB95</key>
		<dict>
			<key>xpos</key>
			<real>630</real>
			<key>ypos</key>
			<real>50</real>
		</dict>
		<key>7466BB6C-FAC1-4334-837E-3B9BA9DED68D</key>
		<dict>
			<key>xpos</key>
			<real>630</real>
			<key>ypos</key>
			<real>810</real>
		</dict>
		<key>774028EF-CE92-4D2B-BAA5-4FFD9DE00FF8</key>
		<dict>
			<key>xpos</key>
			<real>70</real>
			<key>ypos</key>
			<real>810</real>
		</dict>
		<key>7783D7DE-E932-4ACB-B890-4A7EAFF473C2</key>
		<dict>
			<key>xpos</key>
			<real>70</real>
			<key>ypos</key>
			<real>550</real>
		</dict>
		<key>7F6B33E3-0538-4263-85FC-DD755949B570</key>
		<dict>
			<key>xpos</key>
			<real>870</real>
			<key>ypos</key>
			<real>170</real>
		</dict>
		<key>92C1DBD0-E4BB-48D7-99B7-D2C977C9369A</key>
		<dict>
			<key>xpos</key>
			<real>70</real>
			<key>ypos</key>
			<real>680</real>
		</dict>
		<key>B23BD300-027B-4347-B23E-2DAE51EFCCD8</key>
		<dict>
			<key>xpos</key>
			<real>870</real>
			<key>ypos</key>
			<real>810</real>
		</dict>
		<key>CAE17E6D-9008-4467-9FD6-39A943E6AC54</key>
		<dict>
			<key>xpos</key>
			<real>630</real>
			<key>ypos</key>
			<real>290</real>
		</dict>
		<key>DB0D0954-88CC-4CFF-BD37-C84FA2EE4B6F</key>
		<dict>
			<key>xpos</key>
			<real>630</real>
			<key>ypos</key>
			<real>940</real>
		</dict>
		<key>FB48433C-B8B1-4972-8BAB-FF5B28029584</key>
		<dict>
			<key>xpos</key>
			<real>630</real>
			<key>ypos</key>
			<real>680</real>
		</dict>
	</dict>
	<key>userconfigurationconfig</key>
	<array/>
	<key>webaddress</key>
	<string>http://clintonstrong.com</string>
</dict>
</plist>
