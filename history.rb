def find_in_history fb, arg
  arg.sub! /^\.\./, ''
  arg.gsub! /\s+/, '%'
  # prepend '%'
  arg = '%' + arg
  # Do not append '%' if arg ends with '$'
  arg += '%' if not arg.sub!(/\$$/, '')

  history = `sqlite3 -separator ":::" \
    "$HOME/Library/Application Support/Google/Chrome/Default/History.copy" \
    "SELECT distinct title,url FROM urls WHERE title like '#{arg}' OR url like '#{arg}' ORDER BY last_visit_time DESC LIMIT 50 COLLATE NOCASE"`

  urls = fb.items.map {|item| item[:arg] }

  history.force_encoding(Encoding::UTF_8).split("\n").each do |line|
    row   = line.gsub('<', '&lt;'.gsub('>', '&gt;')).gsub('&', '&amp;').split(':::')
    title = row[0]
    url   = row[1]

    next if urls.include? url

    fb.add_item(
      :title => ".. #{title}",
      :subtitle => url,
      :uid => url,
      :arg => url,
      :autocomplete => url,
      :icon => {:name => "/Applications/Google Chrome.app/Contents/Resources/document.icns"})
  end
end

#def history arg
#  s = '<?xml version="1.0"?><items>'

#  history = `sqlite3 -separator ":::" \
#    "$HOME/Library/Application Support/Google/Chrome/Default/History.copy" \
#    "SELECT distinct title,url FROM urls WHERE title like '%#{arg}%' OR url like '%#{arg}%' ORDER BY last_visit_time DESC LIMIT 50 COLLATE NOCASE"`

#  found = false
#  history.force_encoding(Encoding::UTF_8).split("\n").each do |line|
#    found = true
#    row   = line.gsub('<', '&lt;'.gsub('>', '&gt;')).gsub('&', '&amp;').split(':::')
#    title = row[0]
#    url   = row[1]
#    s += "<item>\n"
#    s += "  <arg>#{url}</arg>\n"
#    s += "  <title>#{title}</title>\n"
#    s += "  <subtitle>#{url}</subtitle>\n"
#    s += "  <icon>/Applications/Google Chrome.app/Contents/Resources/document.icns</icon>\n"
#    s += "</item>\n"
#  end

#  if found
#    s + "</items>"
#  end
#end

#if __FILE__ == $0
#  puts history ARGV[0]
#end

