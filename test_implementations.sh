#!/bin/bash

echo "Testing Nim Implementations"
echo "============================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test function
test_implementation() {
    local impl=$1
    local query=$2
    local description=$3
    
    echo -e "\n${YELLOW}Testing $impl with query: '$query' ($description)${NC}"
    
    if [ -x "./$impl" ]; then
        local output=$(timeout 10s ./$impl "$query" 2>/dev/null)
        local exit_code=$?
        
        if [ $exit_code -eq 0 ] && [ -n "$output" ]; then
            local item_count=$(echo "$output" | grep -c "<item")
            echo -e "${GREEN}✓ Success: $item_count items returned${NC}"
            
            # Check for specific patterns
            if [[ "$query" == "1234" ]] && echo "$output" | grep -q "ZEUS-1234"; then
                echo -e "${GREEN}✓ JIRA ticket detection working${NC}"
            fi
            
            if [[ "$query" == "google.com" ]] && echo "$output" | grep -q "http://google.com"; then
                echo -e "${GREEN}✓ URL detection working${NC}"
            fi
            
            return 0
        else
            echo -e "${RED}✗ Failed: No output or error (exit code: $exit_code)${NC}"
            return 1
        fi
    else
        echo -e "${RED}✗ Executable not found: ./$impl${NC}"
        return 1
    fi
}

# Check if executables exist
echo "Checking executables..."
if [ ! -x "./filter" ]; then
    echo -e "${RED}filter executable not found. Run: nim c -d:release filter.nim${NC}"
    exit 1
fi

if [ ! -x "./filter_tabs" ]; then
    echo -e "${RED}filter_tabs executable not found. Run: nim c -d:release filter_tabs.nim${NC}"
    exit 1
fi

echo -e "${GREEN}Both executables found${NC}"

# Test cases
test_cases=(
    "test:Basic search"
    "1234:JIRA ticket detection"
    "google.com:URL detection"
    "github:Common term"
    "abc-123:JIRA project format"
)

failed_tests=0
total_tests=0

# Test both implementations
for impl in "filter" "filter_tabs"; do
    echo -e "\n${YELLOW}=== Testing $impl ===${NC}"
    
    for test_case in "${test_cases[@]}"; do
        IFS=':' read -r query description <<< "$test_case"
        test_implementation "$impl" "$query" "$description"
        
        if [ $? -ne 0 ]; then
            ((failed_tests++))
        fi
        ((total_tests++))
    done
done

# Performance test
echo -e "\n${YELLOW}=== Performance Test ===${NC}"
echo "Running 10 iterations of each implementation..."

echo -n "filter: "
time_filter=$(time (for i in {1..10}; do ./filter test >/dev/null 2>&1; done) 2>&1 | grep real | awk '{print $2}')
echo "$time_filter"

echo -n "filter_tabs: "
time_filter_tabs=$(time (for i in {1..10}; do ./filter_tabs test >/dev/null 2>&1; done) 2>&1 | grep real | awk '{print $2}')
echo "$time_filter_tabs"

# Summary
echo -e "\n${YELLOW}=== Test Summary ===${NC}"
if [ $failed_tests -eq 0 ]; then
    echo -e "${GREEN}All tests passed! ($total_tests/$total_tests)${NC}"
    echo -e "${GREEN}Migration successful - Nim implementations are working correctly${NC}"
else
    echo -e "${RED}$failed_tests/$total_tests tests failed${NC}"
    echo -e "${RED}Please check the issues above before using in production${NC}"
fi

echo -e "\n${YELLOW}Next steps:${NC}"
echo "1. Update your Alfred workflow to use ./filter or ./filter_tabs"
echo "2. Test with your actual workflow in Alfred"
echo "3. Monitor performance improvements"
echo "4. Keep Ruby files as backup until fully validated"
