import std/[os, strutils, osproc]

proc main() =
  let args = commandLineParams()
  let query = args.join(" ").strip().toLower()
  
  # Just get tabs without any processing
  let script = """
tell application "Google Chrome"
  set tabList to {}
  repeat with w from 1 to count of windows
    repeat with t from 1 to count of tabs of window w
      set tabInfo to (URL of tab t of window w) & ":::" & (title of tab t of window w)
      set end of tabList to tabInfo
    end repeat
  end repeat
  return tabList
end tell
"""
  
  try:
    let output = execProcess("osascript -e '" & script.replace("'", "'\"'\"'") & "'")
    let items = output.strip().split(", ")
    
    var matchCount = 0
    for item in items:
      if item.len > 0 and ":::" in item:
        let parts = item.split(":::", 1)
        if parts.len >= 2:
          let title = parts[1].strip().toLower()
          if query in title:
            matchCount += 1
    
    echo "Found " & $items.len & " tabs, " & $matchCount & " matches"
  except:
    echo "Error"

when isMainModule:
  main()
