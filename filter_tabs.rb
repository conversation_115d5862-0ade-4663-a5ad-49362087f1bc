Encoding.default_external = Encoding::UTF_8 if defined? Encoding

require File.expand_path('../alfred_feedback', __FILE__)
require File.expand_path('../browser_tabs', __FILE__)
require File.expand_path('../history', __FILE__)

query = ARGV.join(' ').strip.force_encoding(Encoding::UTF_8).downcase
tabs = BrowserTabs.tabs
fb = Feedback.new

# Search anchored to slashes, dashes, and underscores
def tab_matches_url?(tab, q)
  # Changed by Cao
  #search_regexp = /[\/_-]+#{Regexp.escape(q)}/
  search_regexp = Regexp.new(q)

  (tab.url =~ search_regexp) != nil
end

# Search anchored to the start of words (including CamelCase)
def tab_matches_title?(tab, q)
  #search_regexp = /(\b|[\/\._-])#{q}/
  search_regexp = Regexp.new(q)
  # STDERR.puts 'tab_matches_title?'
  # STDERR.puts search_regexp

  tab.title.downcase =~ search_regexp ||
  # Break CamelCase words into their individual components and search
  tab.title.gsub(/([a-z\d])([A-Z])/,'\1 \2').downcase =~ search_regexp
end

def tab_matches_query?(tab, q)
  ends_with_dollar = q =~ /\$$/

  # Use SPACE to separate parts
  q = q.sub(/\$$/, '').split(/\s+/).map {|item| Regexp.escape(item) }.join('.*')

  q += '$' if ends_with_dollar
  tab_matches_url?(tab, q) || tab_matches_title?(tab, q)
end

def icon_for_tab(tab)
  if tab.browser == "WebKit" || tab.browser == "Safari"
    "/Applications/Safari.app/Contents/Resources/document.icns"
  else
    "/Applications/#{tab.browser}.app/Contents/Resources/document.icns"
  end
end

def is_url? str
  return true if str =~ /^http:|https:/
  return true if str =~ /^([^\s.]+\.)+\w+/
  false
end

tabs.each do |tab|
  next unless tab_matches_query?(tab, query)

  fb.add_item(
    :title => tab.title,
    :subtitle => tab.url,
    :uid => tab.url,
    :arg => tab.url,
    :autocomplete => tab.url,
    :icon => { :name => icon_for_tab(tab) })
end

# If query is a 4-digit string like a Jira ticket number and no ZEUS url matched, add it to the top.
is_jira_ticket = false
if query =~ /^\d{4}$/
  is_jira_ticket = true
  jira_key = "ZEUS-#{query}"
elsif query =~ /^([a-z]+)-?(\d+)$/
  is_jira_ticket = true
  jira_key = "#{$1}-#{$2}".upcase
end

if is_jira_ticket
  found_jira_url = false
  fb.items.each do |item|
    if item[:subtitle] =~ /https:.*#{jira_key}/i
      found_jira_url = true
    end
  end
  if not found_jira_url
    url = "https://jira.vonage.com/browse/#{jira_key}"
    fb.items.unshift({
      :title => jira_key,
      :subtitle => url,
      :uid => url,
      :arg => url,
      :icon => { :name => '/Applications/Google Chrome.app/Contents/Resources/document.icns' }
    })
  end
end

title = ARGV.join(' ').strip.force_encoding(Encoding::UTF_8).gsub('<', '&lt;'.gsub('>', '&gt;')).gsub('&', '&amp;')

if is_url? title
  arg = title
  arg = "http://" + arg if arg !~ /^http/
else
  arg = "https://www.google.com/search?q=#{title.gsub(' ', '+')}"
end
fb.add_item(
  :arg => arg,
  :title => title,
  :icon => {:name => "/Applications/Google Chrome.app/Contents/Resources/document.icns"})

if fb.items.length < 5
  find_in_history(fb, query)
end

xml =
  fb.to_xml
    .sub(/<items>/, "<output><variables><variable name='query'>#{title}</variable></variables><items>")
    .sub(/<\/items>/, '</items></output>')
puts xml
