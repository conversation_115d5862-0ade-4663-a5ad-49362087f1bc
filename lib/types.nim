## Shared types for browser tabs filtering

import std/strutils

type
  Tab* = object
    title*: string
    url*: string
    browser*: string
    window*: int
    index*: int
    domain*: string

  FeedbackItem* = object
    title*: string
    subtitle*: string
    uid*: string
    arg*: string
    autocomplete*: string
    icon*: string
    valid*: string

  Feedback* = object
    items*: seq[FeedbackItem]

# Helper functions for Tab
proc parseDomain*(url: string): string =
  let components = url.split('/')
  if components.len < 3 or components[2] == "":
    return ""
  return components[2].split(':')[0]

proc newTab*(browser, url, title: string, window, index: int): Tab =
  Tab(
    browser: browser,
    window: window,
    index: index,
    url: url,
    title: title,
    domain: parseDomain(url)
  )

# Helper functions for Feedback
proc newFeedback*(): Feedback =
  Feedback(items: @[])

proc addItem*(fb: var Feedback, title, subtitle, uid, arg, autocomplete, icon: string) =
  fb.items.add(FeedbackItem(
    title: title,
    subtitle: subtitle,
    uid: uid,
    arg: arg,
    autocomplete: autocomplete,
    icon: icon,
    valid: "yes"
  ))
