## XML output generation for Alfred workflows

import std/strutils
import types, utils

proc toXml*(fb: Feedback): string =
  var xml = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<items>\n"
  
  for item in fb.items:
    xml.add("  <item uid=\"" & item.uid & "\" arg=\"" & item.arg & "\" valid=\"" & item.valid & "\" autocomplete=\"" & item.autocomplete & "\">\n")
    xml.add("    <title>" & item.title & "</title>\n")
    xml.add("    <subtitle>" & item.subtitle & "</subtitle>\n")
    xml.add("    <icon>" & item.icon & "</icon>\n")
    xml.add("  </item>\n")
  
  xml.add("</items>")
  return xml

proc toXmlWithVariables*(fb: Feedback, query: string): string =
  let title = query.escapeXml()
  var xml = fb.toXml()
  xml = xml.replace("<items>", "<o><variables><variable name='query'>" & title & "</variable></variables><items>")
  xml = xml.replace("</items>", "</items></o>")
  return xml

proc generateFallbackXml*(query: string): string =
  let title = query.escapeXml()
  var arg: string
  
  if isUrl(title):
    arg = title
    if not arg.startsWith("http"):
      arg = "http://" & arg
  else:
    arg = "https://www.google.com/search?q=" & title.replace(" ", "+")
  
  return """<?xml version="1.0" encoding="utf-8"?>
<o>
<variables>
  <variable name="query">""" & title & """</variable>
</variables>
<items>
  <item>
    <arg>""" & arg & """</arg>
    <title>""" & title & """</title>
    <icon>/Applications/Google Chrome.app/Contents/Resources/document.icns</icon>
  </item>
</items>
</o>"""
