## Utility functions

import std/strutils
import types

proc iconForTab*(tab: Tab): string =
  if tab.browser == "WebKit" or tab.browser == "Safari":
    return "/Applications/Safari.app/Contents/Resources/document.icns"
  else:
    return "/Applications/" & tab.browser & ".app/Contents/Resources/document.icns"

proc isUrl*(str: string): bool =
  # Check for http/https prefix
  if str.startsWith("http:") or str.startsWith("https:"):
    return true

  # Check for domain pattern (simple heuristic)
  if '.' in str and not str.startsWith(".") and not str.endsWith("."):
    let parts = str.split('.')
    if parts.len >= 2:
      # Check if it looks like a domain (no spaces, reasonable length)
      for part in parts:
        if ' ' in part or part.len == 0:
          return false
      return true

  return false

proc escapeXml*(text: string): string =
  text.replace("<", "&lt;").replace(">", "&gt;").replace("&", "&amp;")

# JIRA ticket detection without regex
proc detectJiraTicket*(query: string): tuple[isJira: bool, jiraKey: string] =
  # Check for 4-digit number
  if query.len == 4:
    var allDigits = true
    for c in query:
      if not c.isDigit():
        allDigits = false
        break
    if allDigits:
      return (true, "ZEUS-" & query)

  # Check for PROJECT-NUMBER format
  let parts = query.split("-")
  if parts.len == 2:
    let projectPart = parts[0]
    let numberPart = parts[1]

    # Check if project part is all letters and number part is all digits
    var projectValid = projectPart.len > 0
    var numberValid = numberPart.len > 0

    for c in projectPart:
      if not (c >= 'a' and c <= 'z') and not (c >= 'A' and c <= 'Z'):
        projectValid = false
        break

    for c in numberPart:
      if not (c >= '0' and c <= '9'):
        numberValid = false
        break

    if projectValid and numberValid:
      return (true, projectPart.toUpper() & "-" & numberPart)

  # Check for format without dash (e.g., "abc123")
  if query.len > 1:
    var i = 0
    while i < query.len and ((query[i] >= 'a' and query[i] <= 'z') or (query[i] >= 'A' and query[i] <= 'Z')):
      inc i

    if i > 0 and i < query.len:
      var allDigitsAfter = true
      for j in i..<query.len:
        if not (query[j] >= '0' and query[j] <= '9'):
          allDigitsAfter = false
          break

      if allDigitsAfter:
        return (true, query[0..<i].toUpper() & "-" & query[i..^1])

  return (false, "")

proc addJiraTicketIfNeeded*(fb: var Feedback, query: string) =
  let (isJiraTicket, jiraKey) = detectJiraTicket(query)
  
  if isJiraTicket:
    var foundJiraUrl = false
    for item in fb.items:
      if item.subtitle.toLower().find(jiraKey.toLower()) != -1:
        foundJiraUrl = true
        break
    
    if not foundJiraUrl:
      let url = "https://jira.vonage.com/browse/" & jiraKey
      let jiraItem = FeedbackItem(
        title: jiraKey,
        subtitle: url,
        uid: url,
        arg: url,
        autocomplete: url,
        icon: "/Applications/Google Chrome.app/Contents/Resources/document.icns",
        valid: "yes"
      )
      fb.items.insert(jiraItem, 0)  # Add to beginning
