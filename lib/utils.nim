## Utility functions

import std/[re, strutils]
import types

proc iconForTab*(tab: Tab): string =
  if tab.browser == "WebKit" or tab.browser == "Safari":
    return "/Applications/Safari.app/Contents/Resources/document.icns"
  else:
    return "/Applications/" & tab.browser & ".app/Contents/Resources/document.icns"

proc isUrl*(str: string): bool =
  if str.find(re(r"^http:|https:")) != -1:
    return true
  if str.find(re(r"^([^\s.]+\.)+\w+")) != -1:
    return true
  return false

proc escapeXml*(text: string): string =
  text.replace("<", "&lt;").replace(">", "&gt;").replace("&", "&amp;")

# JIRA ticket detection
proc detectJiraTicket*(query: string): tuple[isJira: bool, jiraKey: string] =
  if query.find(re(r"^\d{4}$")) != -1:
    return (true, "ZEUS-" & query)
  elif query.find(re(r"^([a-z]+)-?(\d+)$")) != -1:
    # Simple regex matching for jira key format
    let parts = query.split("-")
    if parts.len == 2:
      return (true, parts[0].toUpper() & "-" & parts[1])
    else:
      # Handle case without dash
      var i = 0
      while i < query.len and query[i] in 'a'..'z':
        inc i
      if i > 0 and i < query.len:
        return (true, query[0..<i].toUpper() & "-" & query[i..^1])
  
  return (false, "")

proc addJiraTicketIfNeeded*(fb: var Feedback, query: string) =
  let (isJiraTicket, jiraKey) = detectJiraTicket(query)
  
  if isJiraTicket:
    var foundJiraUrl = false
    for item in fb.items:
      if item.subtitle.toLower().find(jiraKey.toLower()) != -1:
        foundJiraUrl = true
        break
    
    if not foundJiraUrl:
      let url = "https://jira.vonage.com/browse/" & jiraKey
      let jiraItem = FeedbackItem(
        title: jiraKey,
        subtitle: url,
        uid: url,
        arg: url,
        autocomplete: url,
        icon: "/Applications/Google Chrome.app/Contents/Resources/document.icns",
        valid: "yes"
      )
      fb.items.insert(jiraItem, 0)  # Add to beginning
