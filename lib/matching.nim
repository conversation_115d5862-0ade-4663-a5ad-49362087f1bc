## Tab matching functionality

import std/[re, strutils, sequtils]
import types

# URL matching functions
proc tabMatchesUrl*(tab: Tab, query: string): bool =
  try:
    let regex = re(query)
    return tab.url.find(regex) != -1
  except:
    return false

# Title matching functions  
proc tabMatchesTitle*(tab: Tab, query: string): bool =
  try:
    let searchRegex = re(r"(\b|[\/\._-])" & query)
    let titleLower = tab.title.toLower()
    
    # Direct match
    if titleLower.find(searchRegex) != -1:
      return true
    
    # CamelCase word breaking
    let camelCaseTitle = tab.title.replace(re(r"([a-z\d])([A-Z])"), "$1 $2").toLower()
    return camelCaseTitle.find(searchRegex) != -1
  except:
    return false

# Combined query matching
proc tabMatchesQuery*(tab: Tab, query: string): bool =
  var processedQuery = query
  let endsWithDollar = query.endsWith("$")
  
  if endsWithDollar:
    processedQuery = query[0..^2]  # Remove the $ at the end
  
  # Split query by spaces and escape each part, then join with .*
  let parts = processedQuery.split(re(r"\s+"))
  let escapedParts = parts.map(proc(s: string): string = s.replace(re(r"[.*+?^${}()|[\]\\]"), "\\$0"))
  var regexQuery = escapedParts.join(".*")
  
  if endsWithDollar:
    regexQuery = regexQuery & "$"
  
  return tabMatchesUrl(tab, regexQuery) or tabMatchesTitle(tab, regexQuery)

# Alternative matching for filter_tabs (simpler regex approach)
proc tabMatchesQuerySimple*(tab: Tab, query: string): bool =
  try:
    let regex = re(query)
    return tab.url.find(regex) != -1 or tab.title.toLower().find(regex) != -1 or
           tab.title.replace(re(r"([a-z\d])([A-Z])"), "$1 $2").toLower().find(regex) != -1
  except:
    return false
