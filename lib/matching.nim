## Tab matching functionality - Fast string-based implementation

import std/strutils
import types

# Fast string-based matching without regex
proc containsIgnoreCase(text, pattern: string): bool =
  text.toLower().contains(pattern.toLower())

# URL matching functions
proc tabMatchesUrl*(tab: Tab, query: string): bool =
  return containsIgnoreCase(tab.url, query)

# Title matching functions with word boundary simulation
proc tabMatchesTitle*(tab: Tab, query: string): bool =
  let titleLower = tab.title.toLower()
  let queryLower = query.toLower()

  # Direct substring match
  if queryLower in titleLower:
    return true

  # Check for word boundaries (space, start/end of string, punctuation)
  let wordBoundaryChars = {' ', '.', '_', '-', '/', '\\', '(', ')', '[', ']', '{', '}'}

  # Find all occurrences of the query
  var pos = 0
  while true:
    let found = titleLower.find(queryLower, pos)
    if found == -1:
      break

    # Check if it's at a word boundary
    let atStart = found == 0 or titleLower[found - 1] in wordBoundaryChars
    let atEnd = found + queryLower.len >= titleLower.len or titleLower[found + queryLower.len] in wordBoundaryChars

    if atStart or atEnd:
      return true

    pos = found + 1

  # CamelCase word breaking simulation
  var camelCaseTitle = ""
  for i, c in tab.title:
    if i > 0 and (c >= 'A' and c <= 'Z') and (tab.title[i-1] >= 'a' and tab.title[i-1] <= 'z'):
      camelCaseTitle.add(' ')
    camelCaseTitle.add(c)

  return containsIgnoreCase(camelCaseTitle, query)

# Combined query matching
proc tabMatchesQuery*(tab: Tab, query: string): bool =
  var processedQuery = query
  let endsWithDollar = query.endsWith("$")

  if endsWithDollar:
    processedQuery = query[0..^2]  # Remove the $ at the end

  # Split query by spaces and check all parts
  let parts = processedQuery.split()

  # For multi-word queries, all parts must match
  if parts.len > 1:
    for part in parts:
      if not (tabMatchesUrl(tab, part) or tabMatchesTitle(tab, part)):
        return false
    return true

  # Single word query
  let matches = tabMatchesUrl(tab, processedQuery) or tabMatchesTitle(tab, processedQuery)

  # Handle end-of-line matching for $
  if endsWithDollar:
    return matches and (tab.url.toLower().endsWith(processedQuery.toLower()) or
                       tab.title.toLower().endsWith(processedQuery.toLower()))

  return matches

# Alternative matching for filter_tabs (simpler approach)
proc tabMatchesQuerySimple*(tab: Tab, query: string): bool =
  return containsIgnoreCase(tab.url, query) or containsIgnoreCase(tab.title, query)
