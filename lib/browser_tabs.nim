## Browser tab retrieval functionality

import std/[strutils, osproc]
import types

proc getBrowserTabs*(): seq[Tab] =
  var tabs: seq[Tab] = @[]
  
  # Check if Google Chrome is running
  try:
    let processes = execProcess("ps aux | grep 'Google Chrome' | grep -v grep")
    if processes.strip().len == 0:
      return tabs
  except:
    return tabs
  
  # AppleScript to get Chrome tabs
  let script = """
tell application "Google Chrome"
  set tabList to {}
  repeat with w from 1 to count of windows
    repeat with t from 1 to count of tabs of window w
      set tabInfo to (URL of tab t of window w) & ":::" & (title of tab t of window w)
      set end of tabList to tabInfo
    end repeat
  end repeat
  return tabList
end tell
"""
  
  try:
    let output = execProcess("osascript -e '" & script.replace("'", "'\"'\"'") & "'")
    
    # AppleScript returns comma-separated values, not newline-separated
    let items = output.strip().split(", ")
    
    for i, item in items.pairs():
      if item.len > 0 and ":::" in item:
        let parts = item.split(":::", 1)
        if parts.len >= 2:
          let url = parts[0].strip()
          let title = parts[1].strip()
          tabs.add(newTab("Google Chrome", url, title, 1, i + 1))
  except:
    discard
  
  return tabs
