## Browser history search functionality

import std/[os, strutils, osproc, sequtils]
import types, utils

proc findInHistory*(fb: var Feedback, query: string) =
  var arg = query
  # Remove leading ".." if present
  if arg.startsWith(".."):
    arg = arg[2..^1]

  # Replace spaces with % for SQL LIKE pattern
  arg = arg.replace(" ", "%")
  arg = "%" & arg
  if not arg.endsWith("$"):
    arg = arg & "%"
  else:
    arg = arg[0..^2]  # Remove the $ at the end
  
  let historyPath = getHomeDir() & "/Library/Application Support/Google/Chrome/Default/History.copy"
  let sqlQuery = "SELECT distinct title,url FROM urls WHERE title like '" & arg & "' OR url like '" & arg & "' ORDER BY last_visit_time DESC LIMIT 50 COLLATE NOCASE"
  
  try:
    let cmdResult = execProcess("sqlite3 -separator ':::' '" & historyPath & "' \"" & sqlQuery & "\"")
    let existingUrls = fb.items.map(proc(item: FeedbackItem): string = item.arg)
    
    for line in cmdResult.split("\n"):
      if line.len > 0:
        let parts = line.escapeXml().split(":::")
        if parts.len >= 2:
          let title = parts[0]
          let url = parts[1]
          
          if url notin existingUrls:
            fb.addItem(
              ".. " & title,
              url,
              url,
              url,
              url,
              "/Applications/Google Chrome.app/Contents/Resources/document.icns"
            )
  except:
    discard
