import std/[os, strutils, times]
import lib/[types, browser_tabs, matching, utils, xml_output, history]

proc profileSection(name: string, fn: proc()) =
  let start = cpuTime()
  fn()
  let elapsed = cpuTime() - start
  echo name & ": " & $elapsed & "s"

proc main() =
  let args = commandLineParams()
  let query = args.join(" ").strip().toLower()
  
  var tabs: seq[Tab]
  var fb = newFeedback()
  
  profileSection("Browser tab retrieval") do:
    tabs = getBrowserTabs()
  
  echo "Found " & $tabs.len & " tabs"
  
  profileSection("Tab filtering") do:
    for tab in tabs:
      if tabMatchesQuery(tab, query):
        fb.addItem(
          tab.title,
          tab.url,
          tab.url,
          tab.url,
          tab.url,
          iconForTab(tab)
        )
  
  echo "Matched " & $fb.items.len & " tabs"
  
  profileSection("History search") do:
    if fb.items.len < 10:
      findInHistory(fb, query)
  
  echo "Total items after history: " & $fb.items.len
  
  let title = args.join(" ").strip()
  
  profileSection("JIRA processing") do:
    addJiraTicketIfNeeded(fb, query)
  
  profileSection("XML generation") do:
    if fb.items.len > 0:
      discard toXmlWithVariables(fb, title)
    else:
      discard generateFallbackXml(title)

when isMainModule:
  main()
