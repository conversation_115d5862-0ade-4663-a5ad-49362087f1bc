use std::env;
use browser_tabs_filter::*;

fn main() {
    let args: Vec<String> = env::args().collect();
    let query = if args.len() > 1 {
        args[1..].join(" ").trim().to_lowercase()
    } else {
        String::new()
    };

    let tabs = get_browser_tabs();
    let mut feedback = Feedback::new();

    // Filter tabs using simple matching
    for tab in &tabs {
        if tab_matches_query(tab, &query) {
            feedback.add_item(
                tab.title.clone(),
                tab.url.clone(),
                tab.url.clone(),
                tab.url.clone(),
                tab.url.clone(),
                icon_for_tab(tab),
            );
        }
    }

    // Add JIRA ticket if applicable
    add_jira_ticket_if_needed(&mut feedback, &query);

    // Add fallback search item (always present in filter_tabs)
    let title = if args.len() > 1 {
        args[1..].join(" ").trim().to_string()
    } else {
        String::new()
    };

    let arg = if is_url(&title) {
        if title.starts_with("http") {
            title.clone()
        } else {
            format!("http://{}", title)
        }
    } else {
        format!("https://www.google.com/search?q={}", title.replace(' ', "+"))
    };

    feedback.add_item(
        title.clone(),
        arg.clone(),
        arg.clone(),
        arg.clone(),
        title.clone(),
        "/Applications/Google Chrome.app/Contents/Resources/document.icns".to_string(),
    );

    // Add history if we have less than 5 items
    if feedback.items.len() < 5 {
        find_in_history(&mut feedback, &query);
    }

    // Output XML
    println!("{}", generate_xml(&feedback, &title));
}
