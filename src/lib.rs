use std::process::Command;
use std::collections::HashSet;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Tab {
    pub title: String,
    pub url: String,
    pub browser: String,
    pub window: i32,
    pub index: i32,
}

impl Tab {
    pub fn new(title: String, url: String, browser: String, window: i32, index: i32) -> Self {
        Self { title, url, browser, window, index }
    }
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct FeedbackItem {
    pub title: String,
    pub subtitle: String,
    pub uid: String,
    pub arg: String,
    pub autocomplete: String,
    pub icon: String,
}

pub struct Feedback {
    pub items: Vec<FeedbackItem>,
}

impl Feedback {
    pub fn new() -> Self {
        Self { items: Vec::new() }
    }

    pub fn add_item(&mut self, title: String, subtitle: String, uid: String, arg: String, autocomplete: String, icon: String) {
        self.items.push(FeedbackItem {
            title,
            subtitle,
            uid,
            arg,
            autocomplete,
            icon,
        });
    }
}

pub fn get_browser_tabs() -> Vec<Tab> {
    let mut tabs = Vec::new();

    // Check if Chrome is running
    let ps_output = Command::new("ps")
        .args(&["aux"])
        .output();

    if let Ok(output) = ps_output {
        let output_str = String::from_utf8_lossy(&output.stdout);
        if !output_str.contains("Google Chrome") {
            return tabs;
        }
    } else {
        return tabs;
    }

    // AppleScript to get Chrome tabs
    let script = r#"
tell application "Google Chrome"
  set tabList to {}
  repeat with w from 1 to count of windows
    repeat with t from 1 to count of tabs of window w
      set tabInfo to (URL of tab t of window w) & ":::" & (title of tab t of window w)
      set end of tabList to tabInfo
    end repeat
  end repeat
  return tabList
end tell
"#;

    let output = Command::new("osascript")
        .args(&["-e", script])
        .output();

    if let Ok(output) = output {
        let output_str = String::from_utf8_lossy(&output.stdout);
        let items: Vec<&str> = output_str.trim().split(", ").collect();

        for (i, item) in items.iter().enumerate() {
            if item.contains(":::") {
                let parts: Vec<&str> = item.splitn(2, ":::").collect();
                if parts.len() >= 2 {
                    let url = parts[0].trim().to_string();
                    let title = parts[1].trim().to_string();
                    tabs.push(Tab {
                        title,
                        url,
                        browser: "Google Chrome".to_string(),
                        window: 1,
                        index: i as i32 + 1,
                    });
                }
            }
        }
    }

    tabs
}

pub fn tab_matches_query(tab: &Tab, query: &str) -> bool {
    let query_lower = query.to_lowercase();
    let title_lower = tab.title.to_lowercase();
    let url_lower = tab.url.to_lowercase();

    // Simple substring matching
    if title_lower.contains(&query_lower) || url_lower.contains(&query_lower) {
        return true;
    }

    // Multi-word query support
    if query.contains(' ') {
        let words: Vec<&str> = query.split_whitespace().collect();
        return words.iter().all(|word| {
            let word_lower = word.to_lowercase();
            title_lower.contains(&word_lower) || url_lower.contains(&word_lower)
        });
    }

    false
}

pub fn find_in_history(feedback: &mut Feedback, query: &str) {
    let mut arg = query.to_string();
    
    // Remove leading ".." if present
    if arg.starts_with("..") {
        arg = arg[2..].to_string();
    }
    
    // Replace spaces with % for SQL LIKE pattern
    arg = arg.replace(' ', "%");
    arg = format!("%{}%", arg);
    
    let home_dir = std::env::var("HOME").unwrap_or_default();
    let history_path = format!("{}/Library/Application Support/Google/Chrome/Default/History.copy", home_dir);
    let sql_query = format!(
        "SELECT distinct title,url FROM urls WHERE title like '{}' OR url like '{}' ORDER BY last_visit_time DESC LIMIT 50 COLLATE NOCASE",
        arg, arg
    );

    let output = Command::new("sqlite3")
        .args(&["-separator", ":::", &history_path, &sql_query])
        .output();

    if let Ok(output) = output {
        let output_str = String::from_utf8_lossy(&output.stdout);
        let existing_urls: HashSet<String> = feedback.items.iter().map(|item| item.arg.clone()).collect();

        for line in output_str.lines() {
            if !line.is_empty() {
                let parts: Vec<&str> = line.splitn(2, ":::").collect();
                if parts.len() >= 2 {
                    let title = escape_xml(parts[0]);
                    let url = parts[1].to_string();

                    if !existing_urls.contains(&url) {
                        feedback.add_item(
                            format!(".. {}", title),
                            url.clone(),
                            url.clone(),
                            url.clone(),
                            url.clone(),
                            "/Applications/Google Chrome.app/Contents/Resources/document.icns".to_string(),
                        );
                    }
                }
            }
        }
    }
}

pub fn detect_jira_ticket(query: &str) -> Option<String> {
    // Check for 4-digit number
    if query.len() == 4 && query.chars().all(|c| c.is_ascii_digit()) {
        return Some(format!("ZEUS-{}", query));
    }

    // Check for PROJECT-NUMBER format
    if let Some(dash_pos) = query.find('-') {
        let project_part = &query[..dash_pos];
        let number_part = &query[dash_pos + 1..];

        if !project_part.is_empty() && !number_part.is_empty() &&
           project_part.chars().all(|c| c.is_ascii_alphabetic()) &&
           number_part.chars().all(|c| c.is_ascii_digit()) {
            return Some(format!("{}-{}", project_part.to_uppercase(), number_part));
        }
    }

    // Check for format without dash (e.g., "abc123")
    let mut alpha_end = 0;
    for (i, c) in query.char_indices() {
        if c.is_ascii_alphabetic() {
            alpha_end = i + 1;
        } else {
            break;
        }
    }

    if alpha_end > 0 && alpha_end < query.len() {
        let project_part = &query[..alpha_end];
        let number_part = &query[alpha_end..];

        if number_part.chars().all(|c| c.is_ascii_digit()) {
            return Some(format!("{}-{}", project_part.to_uppercase(), number_part));
        }
    }

    None
}

pub fn add_jira_ticket_if_needed(feedback: &mut Feedback, query: &str) {
    if let Some(jira_key) = detect_jira_ticket(query) {
        let jira_key_lower = jira_key.to_lowercase();
        let found_jira = feedback.items.iter().any(|item| {
            item.subtitle.to_lowercase().contains(&jira_key_lower)
        });

        if !found_jira {
            let url = format!("https://jira.vonage.com/browse/{}", jira_key);
            let jira_item = FeedbackItem {
                title: jira_key,
                subtitle: url.clone(),
                uid: url.clone(),
                arg: url.clone(),
                autocomplete: url.clone(),
                icon: "/Applications/Google Chrome.app/Contents/Resources/document.icns".to_string(),
            };
            feedback.items.insert(0, jira_item);
        }
    }
}

pub fn is_url(s: &str) -> bool {
    s.starts_with("http:") || s.starts_with("https:") || 
    (s.contains('.') && !s.starts_with('.') && !s.ends_with('.') && !s.contains(' '))
}

pub fn escape_xml(text: &str) -> String {
    text.replace('&', "&amp;")
        .replace('<', "&lt;")
        .replace('>', "&gt;")
}

pub fn icon_for_tab(tab: &Tab) -> String {
    if tab.browser == "WebKit" || tab.browser == "Safari" {
        "/Applications/Safari.app/Contents/Resources/document.icns".to_string()
    } else {
        format!("/Applications/{}.app/Contents/Resources/document.icns", tab.browser)
    }
}

pub fn generate_xml(feedback: &Feedback, query: &str) -> String {
    let mut xml = String::from("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n");
    xml.push_str(&format!("<o><variables><variable name='query'>{}</variable></variables><items>\n", escape_xml(query)));

    for item in &feedback.items {
        xml.push_str(&format!(
            "  <item uid=\"{}\" arg=\"{}\" valid=\"yes\" autocomplete=\"{}\">\n",
            escape_xml(&item.uid),
            escape_xml(&item.arg),
            escape_xml(&item.autocomplete)
        ));
        xml.push_str(&format!("    <title>{}</title>\n", escape_xml(&item.title)));
        xml.push_str(&format!("    <subtitle>{}</subtitle>\n", escape_xml(&item.subtitle)));
        xml.push_str(&format!("    <icon>{}</icon>\n", escape_xml(&item.icon)));
        xml.push_str("  </item>\n");
    }

    xml.push_str("</items></o>");
    xml
}

pub fn generate_fallback_xml(query: &str) -> String {
    let escaped_query = escape_xml(query);
    let arg = if is_url(query) {
        if query.starts_with("http") {
            query.to_string()
        } else {
            format!("http://{}", query)
        }
    } else {
        format!("https://www.google.com/search?q={}", query.replace(' ', "+"))
    };

    format!(
        r#"<?xml version="1.0" encoding="utf-8"?>
<o>
<variables>
  <variable name="query">{}</variable>
</variables>
<items>
  <item>
    <arg>{}</arg>
    <title>{}</title>
    <icon>/Applications/Google Chrome.app/Contents/Resources/document.icns</icon>
  </item>
</items>
</o>"#,
        escaped_query, escape_xml(&arg), escaped_query
    )
}
