use std::env;
use browser_tabs_filter::*;

fn main() {
    let args: Vec<String> = env::args().collect();
    let query = if args.len() > 1 {
        args[1..].join(" ").trim().to_lowercase()
    } else {
        String::new()
    };

    let tabs = get_browser_tabs();
    let mut feedback = Feedback::new();

    // Filter tabs
    for tab in &tabs {
        if tab_matches_query(tab, &query) {
            feedback.add_item(
                tab.title.clone(),
                tab.url.clone(),
                tab.url.clone(),
                tab.url.clone(),
                tab.url.clone(),
                icon_for_tab(tab),
            );
        }
    }

    // Add history if we have less than 10 items
    if feedback.items.len() < 10 {
        find_in_history(&mut feedback, &query);
    }

    let title = if args.len() > 1 {
        args[1..].join(" ").trim().to_string()
    } else {
        String::new()
    };

    // Add JIRA ticket if applicable
    add_jira_ticket_if_needed(&mut feedback, &query);

    // Output results
    if !feedback.items.is_empty() {
        println!("{}", generate_xml(&feedback, &title));
    } else {
        println!("{}", generate_fallback_xml(&title));
    }
}
