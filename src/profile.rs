use std::env;
use std::time::Instant;
use browser_tabs_filter::*;

fn main() {
    let args: Vec<String> = env::args().collect();
    let query = if args.len() > 1 {
        args[1..].join(" ").trim().to_lowercase()
    } else {
        String::new()
    };

    println!("Profiling Rust implementation with query: '{}'", query);
    println!("{}", "=".repeat(50));

    let total_start = Instant::now();

    // 1. Browser tab retrieval
    let start = Instant::now();
    let tabs = get_browser_tabs();
    let tab_time = start.elapsed();
    println!("1. Browser tab retrieval: {:.3}s ({} tabs)", tab_time.as_secs_f64(), tabs.len());

    // 2. Tab filtering
    let start = Instant::now();
    let mut feedback = Feedback::new();
    for tab in &tabs {
        if tab_matches_query(tab, &query) {
            feedback.add_item(
                tab.title.clone(),
                tab.url.clone(),
                tab.url.clone(),
                tab.url.clone(),
                tab.url.clone(),
                icon_for_tab(tab),
            );
        }
    }
    let filter_time = start.elapsed();
    println!("2. Tab filtering: {:.3}s ({} matches)", filter_time.as_secs_f64(), feedback.items.len());

    // 3. History search (if needed)
    let start = Instant::now();
    let history_time = if feedback.items.len() < 10 {
        find_in_history(&mut feedback, &query);
        start.elapsed()
    } else {
        std::time::Duration::from_secs(0)
    };
    println!("3. History search: {:.3}s ({} total items)", history_time.as_secs_f64(), feedback.items.len());

    // 4. JIRA processing
    let start = Instant::now();
    add_jira_ticket_if_needed(&mut feedback, &query);
    let jira_time = start.elapsed();
    println!("4. JIRA processing: {:.3}s", jira_time.as_secs_f64());

    // 5. XML generation
    let start = Instant::now();
    let title = if args.len() > 1 {
        args[1..].join(" ").trim().to_string()
    } else {
        String::new()
    };
    let _xml = if !feedback.items.is_empty() {
        generate_xml(&feedback, &title)
    } else {
        generate_fallback_xml(&title)
    };
    let xml_time = start.elapsed();
    println!("5. XML generation: {:.3}s", xml_time.as_secs_f64());

    let total_time = total_start.elapsed();
    println!("{}", "=".repeat(50));
    println!("TOTAL TIME: {:.3}s", total_time.as_secs_f64());
    
    // Calculate percentages
    let tab_pct = (tab_time.as_secs_f64() / total_time.as_secs_f64()) * 100.0;
    let filter_pct = (filter_time.as_secs_f64() / total_time.as_secs_f64()) * 100.0;
    let history_pct = (history_time.as_secs_f64() / total_time.as_secs_f64()) * 100.0;
    let jira_pct = (jira_time.as_secs_f64() / total_time.as_secs_f64()) * 100.0;
    let xml_pct = (xml_time.as_secs_f64() / total_time.as_secs_f64()) * 100.0;
    
    println!("\nBREAKDOWN:");
    println!("- Browser tabs: {:.1}%", tab_pct);
    println!("- Tab filtering: {:.1}%", filter_pct);
    println!("- History search: {:.1}%", history_pct);
    println!("- JIRA processing: {:.1}%", jira_pct);
    println!("- XML generation: {:.1}%", xml_pct);
}
