use std::env;
use std::time::Instant;
use browser_tabs_filter::*;

fn main() {
    let args: Vec<String> = env::args().collect();
    let query = if args.len() > 1 {
        args[1..].join(" ").trim().to_lowercase()
    } else {
        String::new()
    };

    println!("Testing ONLY our Rust code (no AppleScript/SQLite):");
    
    let start = Instant::now();
    
    // Simulate processing 100 tabs
    let mut all_tabs = Vec::new();
    for i in 0..100 {
        all_tabs.push(browser_tabs_filter::Tab::new(
            format!("Tab {} with test content", i),
            format!("https://example{}.com/test", i),
            "Google Chrome".to_string(),
            1,
            i as i32,
        ));
    }
    
    let mut feedback = Feedback::new();
    
    // Test our filtering logic
    for tab in &all_tabs {
        if tab_matches_query(tab, &query) {
            feedback.add_item(
                tab.title.clone(),
                tab.url.clone(),
                tab.url.clone(),
                tab.url.clone(),
                tab.url.clone(),
                icon_for_tab(tab),
            );
        }
    }
    
    // Test JIRA detection
    add_jira_ticket_if_needed(&mut feedback, &query);
    
    // Test XML generation
    let title = args.join(" ");
    let _xml = if !feedback.items.is_empty() {
        generate_xml(&feedback, &title)
    } else {
        generate_fallback_xml(&title)
    };
    
    let elapsed = start.elapsed();
    println!("Pure Rust processing time: {:.6}s", elapsed.as_secs_f64());
    println!("Found {} matches", feedback.items.len());
    
    // Show how fast this could be if we could eliminate I/O
    println!("\nIf we could eliminate AppleScript/SQLite overhead:");
    println!("- Current total time: ~0.643s");
    println!("- Pure Rust time: {:.6}s", elapsed.as_secs_f64());
    println!("- Theoretical speedup: {:.0}x faster!", 0.643 / elapsed.as_secs_f64());
}
