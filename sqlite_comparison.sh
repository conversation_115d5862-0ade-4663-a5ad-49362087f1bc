#!/bin/bash

echo "SQLite Performance Comparison: Shell vs Gem"
echo "============================================"

# Test the optimized version (SQLite3 gem)
echo "Testing SQLite3 gem version (5 runs):"
total_gem=0
for i in {1..5}; do
    start_time=$(date +%s.%N)
    ruby filter.rb test > /dev/null 2>&1
    end_time=$(date +%s.%N)
    duration=$(echo "$end_time - $start_time" | bc)
    echo "  Run $i: ${duration}s"
    total_gem=$(echo "$total_gem + $duration" | bc)
done
avg_gem=$(echo "scale=3; $total_gem / 5" | bc)
echo "  Average: ${avg_gem}s"

# Switch to original version
cp history.rb history_optimized_backup.rb
cat > history.rb << 'EOF'
def find_in_history fb, arg
  arg.sub! /^\.\./, ''
  arg.gsub! /\s+/, '%'
  # prepend '%'
  arg = '%' + arg
  # Do not append '%' if arg ends with '$'
  arg += '%' if not arg.sub!(/\$$/, '')

  history = `sqlite3 -separator ":::" \
    "$HOME/Library/Application Support/Google/Chrome/Default/History.copy" \
    "SELECT distinct title,url FROM urls WHERE title like '#{arg}' OR url like '#{arg}' ORDER BY last_visit_time DESC LIMIT 50 COLLATE NOCASE"`

  urls = fb.items.map {|item| item[:arg] }

  history.force_encoding(Encoding::UTF_8).split("\n").each do |line|
    row   = line.gsub('<', '&lt;'.gsub('>', '&gt;')).gsub('&', '&amp;').split(':::')
    title = row[0]
    url   = row[1]

    next if urls.include? url

    fb.add_item(
      :title => ".. #{title}",
      :subtitle => url,
      :uid => url,
      :arg => url,
      :autocomplete => url,
      :icon => {:name => "/Applications/Google Chrome.app/Contents/Resources/document.icns"})
  end
end
EOF

echo
echo "Testing sqlite3 executable version (5 runs):"
total_shell=0
for i in {1..5}; do
    start_time=$(date +%s.%N)
    ruby filter.rb test > /dev/null 2>&1
    end_time=$(date +%s.%N)
    duration=$(echo "$end_time - $start_time" | bc)
    echo "  Run $i: ${duration}s"
    total_shell=$(echo "$total_shell + $duration" | bc)
done
avg_shell=$(echo "scale=3; $total_shell / 5" | bc)
echo "  Average: ${avg_shell}s"

# Restore optimized version
cp history_optimized_backup.rb history.rb

echo
echo "=========================================="
echo "RESULTS:"
echo "SQLite3 gem:    ${avg_gem}s"
echo "sqlite3 shell:  ${avg_shell}s"

# Calculate improvement
if (( $(echo "$avg_shell > $avg_gem" | bc -l) )); then
    improvement=$(echo "scale=1; $avg_shell / $avg_gem" | bc)
    echo "Improvement:    ${improvement}x faster with SQLite3 gem"
else
    slowdown=$(echo "scale=1; $avg_gem / $avg_shell" | bc)
    echo "Result:         ${slowdown}x slower with SQLite3 gem"
fi
