require 'browser_tabs/commands/url_command'

module BrowserTabs
  class ActivateTab < UrlCommand
    def prefix
      "set _tab_was_found to false"
    end

    def tab_body
      if @app_name == 'Safari' || @app_name == 'WebKit'
        active_tab_keyword = 'current tab to tab'
      else
        active_tab_keyword = 'active tab index to '
      end

      <<-APPLESCRIPT

        set _tab_was_found to true
        activate
        tell _window
          set #{active_tab_keyword} _tab_index
          set index to 1
        end tell
        exit repeat
      APPLESCRIPT
    end

    def after_browser
      <<-APPLESCRIPT
        if (_tab_was_found) then
          -- Bring window to front
          tell application "System Events" to tell process "#{@app_name}"
            perform action "AXRaise" of window 1
            -- account for instances when the window doesn't switch fast enough
            delay 0.5
            perform action "AXRaise" of window 1
            -- Prevent other running browsers from potentially activating
            return
          end tell
        else
          tell application "Google Chrome"
            -- activate default window which has Google Calendar tab
            set _window_index to 1
            repeat with _window in windows
              try
                set _tabs to a reference to tabs of _window
                set _tab_count to count of _tabs
                set _tab_index to 1
                repeat with _tab in _tabs
                  if "jira.vonage.com" is in (URL of _tab as string) then
                    tell application "System Events" to tell process "Google Chrome"
                      perform action "AXRaise" of window _window_index
                      set frontmost to true
                    end tell
                  end if
                  set _tab_index to _tab_index + 1
                end repeat
              end try
              set _window_index to _window_index + 1
            end repeat

            open location "#{@url.gsub('"', '\\"')}"
            delay 0.4
            activate
          end tell
        end if
      APPLESCRIPT
    end
  end
end
