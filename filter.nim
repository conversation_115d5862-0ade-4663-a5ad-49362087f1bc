import std/[os, strutils, sequtils, re, osproc]

type
  Tab = object
    title: string
    url: string
    browser: string
    window: int
    index: int
    domain: string

  FeedbackItem = object
    title: string
    subtitle: string
    uid: string
    arg: string
    autocomplete: string
    icon: string
    valid: string

  Feedback = object
    items: seq[FeedbackItem]

# Helper functions
proc parseDomain(url: string): string =
  let components = url.split('/')
  if components.len < 3 or components[2] == "":
    return ""
  return components[2].split(':')[0]

proc newTab(browser, url, title: string, window, index: int): Tab =
  Tab(
    browser: browser,
    window: window,
    index: index,
    url: url,
    title: title,
    domain: parseDomain(url)
  )

proc newFeedback(): Feedback =
  Feedback(items: @[])

proc addItem(fb: var Feedback, title, subtitle, uid, arg, autocomplete, icon: string) =
  fb.items.add(FeedbackItem(
    title: title,
    subtitle: subtitle,
    uid: uid,
    arg: arg,
    autocomplete: autocomplete,
    icon: icon,
    valid: "yes"
  ))

# Browser tab retrieval functions
proc getBrowserTabs(): seq[Tab] =
  var tabs: seq[Tab] = @[]

  # Check if Google Chrome is running
  try:
    let processes = execProcess("ps aux | grep 'Google Chrome' | grep -v grep")
    if processes.strip().len == 0:
      return tabs
  except:
    return tabs

  # AppleScript to get Chrome tabs
  let script = """
tell application "Google Chrome"
  set tabList to {}
  repeat with w from 1 to count of windows
    repeat with t from 1 to count of tabs of window w
      set tabInfo to (URL of tab t of window w) & ":::" & (title of tab t of window w)
      set end of tabList to tabInfo
    end repeat
  end repeat
  return tabList
end tell
"""

  try:
    let output = execProcess("osascript -e '" & script.replace("'", "'\"'\"'") & "'")

    # AppleScript returns comma-separated values, not newline-separated
    let items = output.strip().split(", ")

    for i, item in items.pairs():
      if item.len > 0 and ":::" in item:
        let parts = item.split(":::", 1)
        if parts.len >= 2:
          let url = parts[0].strip()
          let title = parts[1].strip()
          tabs.add(newTab("Google Chrome", url, title, 1, i + 1))
  except:
    discard

  return tabs

# Matching functions
proc tabMatchesUrl(tab: Tab, query: string): bool =
  try:
    let regex = re(query)
    return tab.url.find(regex) != -1
  except:
    return false

proc tabMatchesTitle(tab: Tab, query: string): bool =
  try:
    let searchRegex = re(r"(\b|[\/\._-])" & query)
    let titleLower = tab.title.toLower()

    # Direct match
    if titleLower.find(searchRegex) != -1:
      return true

    # CamelCase word breaking
    let camelCaseTitle = tab.title.replace(re(r"([a-z\d])([A-Z])"), "$1 $2").toLower()
    return camelCaseTitle.find(searchRegex) != -1
  except:
    return false

proc tabMatchesQuery(tab: Tab, query: string): bool =
  # Split query by spaces and escape each part, then join with .*
  let parts = query.split(re(r"\s+"))
  let escapedParts = parts.map(proc(s: string): string = s.replace(re(r"[.*+?^${}()|[\]\\]"), "\\$0"))
  let regexQuery = escapedParts.join(".*")

  return tabMatchesUrl(tab, regexQuery) or tabMatchesTitle(tab, regexQuery)

proc iconForTab(tab: Tab): string =
  if tab.browser == "WebKit" or tab.browser == "Safari":
    return "/Applications/Safari.app/Contents/Resources/document.icns"
  else:
    return "/Applications/" & tab.browser & ".app/Contents/Resources/document.icns"

proc isUrl(str: string): bool =
  if str.find(re(r"^http:|https:")) != -1:
    return true
  if str.find(re(r"^([^\s.]+\.)+\w+")) != -1:
    return true
  return false

# History search function
proc findInHistory(fb: var Feedback, query: string) =
  var arg = query
  arg = arg.replace(re(r"^\.\."), "")
  arg = arg.replace(re(r"\s+"), "%")
  arg = "%" & arg
  if not arg.endsWith("$"):
    arg = arg & "%"
  else:
    arg = arg[0..^2]  # Remove the $ at the end

  let historyPath = getHomeDir() & "/Library/Application Support/Google/Chrome/Default/History.copy"
  let sqlQuery = "SELECT distinct title,url FROM urls WHERE title like '" & arg & "' OR url like '" & arg & "' ORDER BY last_visit_time DESC LIMIT 50 COLLATE NOCASE"

  try:
    let cmdResult = execProcess("sqlite3 -separator ':::' '" & historyPath & "' \"" & sqlQuery & "\"")
    let existingUrls = fb.items.map(proc(item: FeedbackItem): string = item.arg)

    for line in cmdResult.split("\n"):
      if line.len > 0:
        let parts = line.replace("<", "&lt;").replace(">", "&gt;").replace("&", "&amp;").split(":::")
        if parts.len >= 2:
          let title = parts[0]
          let url = parts[1]

          if url notin existingUrls:
            fb.addItem(
              ".. " & title,
              url,
              url,
              url,
              url,
              "/Applications/Google Chrome.app/Contents/Resources/document.icns"
            )
  except:
    discard

# XML generation
proc toXml(fb: Feedback): string =
  var xml = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<items>\n"

  for item in fb.items:
    xml.add("  <item uid=\"" & item.uid & "\" arg=\"" & item.arg & "\" valid=\"" & item.valid & "\" autocomplete=\"" & item.autocomplete & "\">\n")
    xml.add("    <title>" & item.title & "</title>\n")
    xml.add("    <subtitle>" & item.subtitle & "</subtitle>\n")
    xml.add("    <icon>" & item.icon & "</icon>\n")
    xml.add("  </item>\n")

  xml.add("</items>")
  return xml

# Main execution
proc main() =
  let args = commandLineParams()
  let query = args.join(" ").strip().toLower()
  let tabs = getBrowserTabs()
  var fb = newFeedback()

  # Filter tabs
  for tab in tabs:
    if tabMatchesQuery(tab, query):
      fb.addItem(
        tab.title,
        tab.url,
        tab.url,
        tab.url,
        tab.url,
        iconForTab(tab)
      )

  # Add history if we have less than 10 items
  if fb.items.len < 10:
    findInHistory(fb, query)

  let title = args.join(" ").strip().replace("<", "&lt;").replace(">", "&gt;").replace("&", "&amp;")

  # Jira ticket handling
  var isJiraTicket = false
  var jiraKey = ""

  if query.find(re(r"^\d{4}$")) != -1:
    isJiraTicket = true
    jiraKey = "ZEUS-" & query
  elif query.find(re(r"^([a-z]+)-?(\d+)$")) != -1:
    isJiraTicket = true
    # Simple regex matching for jira key format
    let parts = query.split("-")
    if parts.len == 2:
      jiraKey = parts[0].toUpper() & "-" & parts[1]
    else:
      # Handle case without dash
      var i = 0
      while i < query.len and query[i] in 'a'..'z':
        inc i
      if i > 0 and i < query.len:
        jiraKey = query[0..<i].toUpper() & "-" & query[i..^1]

  if isJiraTicket:
    var foundJiraUrl = false
    for item in fb.items:
      if item.subtitle.toLower().find(jiraKey.toLower()) != -1:
        foundJiraUrl = true
        break

    if not foundJiraUrl:
      let url = "https://jira.vonage.com/browse/" & jiraKey
      let jiraItem = FeedbackItem(
        title: jiraKey,
        subtitle: url,
        uid: url,
        arg: url,
        autocomplete: url,
        icon: "/Applications/Google Chrome.app/Contents/Resources/document.icns",
        valid: "yes"
      )
      fb.items.insert(jiraItem, 0)  # Add to beginning

  # Output results
  if fb.items.len > 0:
    var xml = fb.toXml()
    xml = xml.replace("<items>", "<o><variables><variable name='query'>" & title & "</variable></variables><items>")
    xml = xml.replace("</items>", "</items></o>")
    echo xml
  else:
    var arg: string
    if isUrl(title):
      arg = title
      if not arg.startsWith("http"):
        arg = "http://" & arg
    else:
      arg = "https://www.google.com/search?q=" & title.replace(" ", "+")

    echo """<?xml version="1.0" encoding="utf-8"?>
<o>
<variables>
  <variable name="query">""" & title & """</variable>
</variables>
<items>
  <item>
    <arg>""" & arg & """</arg>
    <title>""" & title & """</title>
    <icon>/Applications/Google Chrome.app/Contents/Resources/document.icns</icon>
  </item>
</items>
</o>"""

when isMainModule:
  main()