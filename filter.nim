import std/[os, strutils, sequtils, re, xmltree, strtabs]

type
  Tab = object
    title: string
    url: string
    browser: string

  FeedbackItem = object
    title: string
    subtitle: string
    uid: string
    arg: string
    autocomplete: string
    icon: string

proc tabMatchesUrl(tab: Tab, q: string): bool =
  q in tab.url.toLowerAscii

proc tabMatchesTitle(tab: Tab, q: string): bool =
  let searchRegexp = re("(\b|[/._-])" & q)
  tab.title.toLowerAscii.contains(searchRegexp) or
    tab.title.replace(re"([a-z\d])([A-Z])", "$1 $2").toLowerAscii.contains(searchRegexp)

proc tabMatchesQuery(tab: Tab, q: string): bool =
  let parts = q.split(re"\s+").mapIt(re.escapeRe(it)).join(".*")
  tabMatchesUrl(tab, parts) or tabMatchesTitle(tab, parts)

proc iconForTab(tab: Tab): string =
  if tab.browser in ["WebKit", "Safari"]:
    "/Applications/Safari.app/Contents/Resources/document.icns"
  else:
    "/Applications/" & tab.browser & ".app/Contents/Resources/document.icns"

proc isUrl(s: string): bool =
  s.startsWith("http:") or s.startsWith("https:") or s.contains(re"^([^\s.]+\.)+\w+")

proc createFeedbackItem(tab: Tab): FeedbackItem =
  FeedbackItem(
    title: tab.title,
    subtitle: tab.url,
    uid: tab.url,
    arg: tab.url,
    autocomplete: tab.url,
    icon: iconForTab(tab)
  )

proc generateXml(items: seq[FeedbackItem], query: string): string =
  let root = newElement("output")
  let variables = newElement("variables")
  let queryVar = newElement("variable")
  queryVar.attrs = {"name": "query"}.toXmlAttributes
  queryVar.add newText(query)
  variables.add queryVar
  root.add variables

  let itemsElem = newElement("items")
  for item in items:
    let itemElem = newElement("item")
    let titleElem = newElement("title")
    titleElem.add newText(item.title)
    itemElem.add titleElem
    
    let subtitleElem = newElement("subtitle")
    subtitleElem.add newText(item.subtitle)
    itemElem.add subtitleElem
    
    let uidElem = newElement("uid")
    uidElem.add newText(item.uid)
    itemElem.add uidElem
    
    let argElem = newElement("arg")
    argElem.add newText(item.arg)
    itemElem.add argElem
    
    let autocompleteElem = newElement("autocomplete")
    autocompleteElem.add newText(item.autocomplete)
    itemElem.add autocompleteElem
    
    let iconElem = newElement("icon")
    iconElem.add newText(item.icon)
    itemElem.add iconElem
    
    itemsElem.add itemElem
  
  root.add itemsElem
  $root

proc main() =
  let query = commandLineParams().join(" ").strip.toLowerAscii
  
  # TODO: Implement BrowserTabs.tabs equivalent in Nim
  let tabs: seq[Tab] = @[]  # Replace with actual tab data
  
  var feedbackItems: seq[FeedbackItem] = @[]
  
  for tab in tabs:
    if tabMatchesQuery(tab, query):
      feedbackItems.add(createFeedbackItem(tab))
  
  if feedbackItems.len < 10:
    # TODO: Implement find_in_history equivalent in Nim
    discard

  # Handle JIRA ticket logic
  let jiraTicketRegex = re"^([a-z]+)-?(\d+)$"
  var jiraKey = ""
  if query.match(re"^\d{4}$"):
    jiraKey = "ZEUS-" & query
  elif query.match(jiraTicketRegex):
    let matches = query.findAll(jiraTicketRegex)
    jiraKey = matches[0].toUpperAscii & "-" & matches[1]
  
  if jiraKey != "":
    let jiraUrl = "https://jira.vonage.com/browse/" & jiraKey
    if not feedbackItems.anyIt(it.subtitle.contains(jiraKey)):
      feedbackItems.insert(FeedbackItem(
        title: jiraKey,
        subtitle: jiraUrl,
        uid: jiraUrl,
        arg: jiraUrl,
        icon: "/Applications/Google Chrome.app/Contents/Resources/document.icns"
      ), 0)

  if feedbackItems.len > 0:
    echo generateXml(feedbackItems, query)
  else:
    var arg = query
    if isUrl(query):
      if not query.startsWith("http"):
        arg = "http://" & arg
    else:
      arg = "https://www.google.com/search?q=" & query.replace(" ", "+")
    
    let fallbackItem = @[FeedbackItem(
      title: query,
      arg: arg,
      icon: "/Applications/Google Chrome.app/Contents/Resources/document.icns"
    )]
    echo generateXml(fallbackItem, query)

when isMainModule:
  main()