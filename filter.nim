import std/[os, strutils]
import lib/[types, browser_tabs, matching, utils, xml_output, history]



# Main execution
proc main() =
  let args = commandLineParams()
  let query = args.join(" ").strip().toLower()
  let tabs = getBrowserTabs()
  var fb = newFeedback()

  # Filter tabs
  for tab in tabs:
    if tabMatchesQuery(tab, query):
      fb.addItem(
        tab.title,
        tab.url,
        tab.url,
        tab.url,
        tab.url,
        iconForTab(tab)
      )

  # Add history if we have less than 10 items
  if fb.items.len < 10:
    findInHistory(fb, query)

  let title = args.join(" ").strip()

  # Add JIRA ticket if applicable
  addJiraTicketIfNeeded(fb, query)

  # Output results
  if fb.items.len > 0:
    echo toXmlWithVariables(fb, title)
  else:
    echo generateFallbackXml(title)

when isMainModule:
  main()