#!/bin/bash

echo "Performance Comparison: <PERSON> vs <PERSON><PERSON> vs Rust"
echo "============================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test query
QUERY="test"
ITERATIONS=5

echo -e "\nTesting with query: '$QUERY' ($ITERATIONS iterations each)\n"

# Function to run performance test
run_test() {
    local name=$1
    local command=$2
    local color=$3
    
    echo -e "${color}Testing $name:${NC}"
    
    local total_time=0
    for i in $(seq 1 $ITERATIONS); do
        local start_time=$(date +%s.%N)
        eval "$command" > /dev/null 2>&1
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc)
        total_time=$(echo "$total_time + $duration" | bc)
        echo "  Run $i: ${duration}s"
    done
    
    local avg_time=$(echo "scale=3; $total_time / $ITERATIONS" | bc)
    echo -e "  ${color}Average: ${avg_time}s${NC}"
    echo
    
    # Return average time for comparison
    echo "$avg_time"
}

# Test Ruby
echo "=== Ruby Implementation ==="
ruby_time=$(run_test "Ruby filter.rb" "ruby filter.rb $QUERY" "$RED")

# Test Nim (if available)
if [ -x "./filter" ]; then
    echo "=== Nim Implementation ==="
    nim_time=$(run_test "Nim filter" "./filter $QUERY" "$YELLOW")
else
    echo -e "${YELLOW}Nim implementation not found${NC}"
    nim_time="N/A"
fi

# Test Rust
echo "=== Rust Implementation ==="
rust_time=$(run_test "Rust filter" "./target/release/filter $QUERY" "$GREEN")

# Summary
echo "=========================================="
echo "PERFORMANCE SUMMARY"
echo "=========================================="
echo -e "${RED}Ruby:${NC}    ${ruby_time}s"
if [ "$nim_time" != "N/A" ]; then
    echo -e "${YELLOW}Nim:${NC}     ${nim_time}s"
fi
echo -e "${GREEN}Rust:${NC}    ${rust_time}s"

# Calculate improvements
if [ "$nim_time" != "N/A" ]; then
    nim_improvement=$(echo "scale=1; $ruby_time / $nim_time" | bc)
    echo -e "\n${YELLOW}Nim vs Ruby:${NC} ${nim_improvement}x improvement"
fi

rust_improvement=$(echo "scale=1; $ruby_time / $rust_time" | bc)
echo -e "${GREEN}Rust vs Ruby:${NC} ${rust_improvement}x improvement"

if [ "$nim_time" != "N/A" ]; then
    rust_vs_nim=$(echo "scale=1; $nim_time / $rust_time" | bc)
    echo -e "${BLUE}Rust vs Nim:${NC} ${rust_vs_nim}x improvement"
fi

echo
echo "🏆 Winner: Rust implementation!"
echo "✅ Ready for production use"
