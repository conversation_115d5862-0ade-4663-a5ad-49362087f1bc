# Nim Implementation of Browser Tabs Filter

This document describes the Nim implementation of the browser tabs filtering functionality, originally written in Ruby (`filter.rb`).

## Overview

The Nim implementation (`filter.nim`) provides the same functionality as the Ruby version but with significantly improved performance. It filters browser tabs and browser history based on user queries and outputs results in Alfred workflow XML format.

## Features

### Core Functionality
- **Browser Tab Filtering**: Searches through open Google Chrome tabs
- **History Search**: Searches Chrome browser history when fewer than 10 tab matches are found
- **URL Matching**: Matches queries against tab URLs using regex patterns
- **Title Matching**: Matches queries against tab titles with word boundary detection and CamelCase support
- **JIRA Integration**: Automatically detects JIRA ticket patterns and generates JIRA URLs
- **Fallback Search**: Provides Google search or direct URL navigation when no matches are found

### JIRA Ticket Support
- Detects 4-digit numbers and converts them to `ZEUS-XXXX` format
- Recognizes `PROJECT-NUMBER` patterns (e.g., `ABC-123`, `abc123`)
- Generates direct links to Jira tickets at `https://jira.vonage.com/browse/`

### URL Detection
- Recognizes HTTP/HTTPS URLs
- Detects domain patterns (e.g., `google.com`, `github.io`)
- Automatically adds `http://` prefix when needed

## Performance Improvements

The Nim implementation offers several performance advantages over Ruby:

1. **Compiled vs Interpreted**: Nim compiles to native machine code, eliminating interpreter overhead
2. **Memory Efficiency**: Lower memory footprint and better garbage collection
3. **Startup Time**: Faster startup time due to no interpreter initialization
4. **String Processing**: More efficient string operations and regex processing

### Benchmarking

Run the included benchmark script to compare performance:

```bash
./benchmark.sh
```

Expected improvements:
- **2-5x faster** execution time for typical queries
- **Significantly lower** memory usage
- **Faster startup** time, especially important for Alfred workflows

## Implementation Details

### Key Components

1. **Tab Structure**: Represents browser tabs with title, URL, browser, window, and index information
2. **Feedback System**: Manages Alfred workflow items with proper XML formatting
3. **Browser Integration**: Uses AppleScript to retrieve Chrome tab information
4. **History Integration**: Queries Chrome's SQLite history database
5. **Pattern Matching**: Implements sophisticated regex-based matching for URLs and titles

### Matching Algorithm

The matching algorithm follows the same logic as the Ruby version:

1. **Query Processing**: Splits multi-word queries and creates regex patterns
2. **URL Matching**: Direct regex matching against tab URLs
3. **Title Matching**: Word boundary and CamelCase-aware title matching
4. **Combined Scoring**: Matches tabs that satisfy either URL or title criteria

### XML Output

Generates Alfred-compatible XML with:
- Item metadata (title, subtitle, UID, argument)
- Icon paths for different browsers
- Query variables for Alfred workflow state
- Proper XML escaping for special characters

## Building and Usage

### Prerequisites
- Nim compiler (version 2.0+)
- macOS with Google Chrome
- SQLite3 command-line tool

### Building
```bash
# Debug build
nim c filter.nim

# Optimized release build
nim c -d:release filter.nim
```

### Usage
```bash
# Search for tabs matching "github"
./filter github

# Search for JIRA ticket
./filter 1234

# Search for URL
./filter google.com
```

## Migration from Ruby

To migrate from the Ruby implementation:

1. **Compile the Nim version**: `nim c -d:release filter.nim`
2. **Update Alfred workflow**: Change the script command from `ruby filter.rb` to `./filter`
3. **Test functionality**: Verify all features work as expected
4. **Monitor performance**: Observe improved response times

## Dependencies

The Nim implementation uses only standard library modules:
- `std/os`: Command-line argument processing and file system operations
- `std/strutils`: String manipulation and processing
- `std/sequtils`: Sequence operations and functional programming
- `std/re`: Regular expression support
- `std/osproc`: External process execution (AppleScript, SQLite)

## Error Handling

The implementation includes robust error handling:
- Graceful fallback when Chrome is not running
- Safe AppleScript execution with exception handling
- SQLite query error recovery
- Regex compilation error handling

## Future Enhancements

Potential improvements for the Nim implementation:
- Support for additional browsers (Safari, Firefox)
- Caching of browser tab information
- Fuzzy matching algorithms
- Configuration file support
- Parallel processing for large tab sets

## Compatibility

The Nim implementation maintains full compatibility with:
- Alfred workflow XML format
- Original Ruby functionality
- JIRA URL patterns
- Chrome history database format
- AppleScript browser integration
