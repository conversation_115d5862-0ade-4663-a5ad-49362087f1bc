require 'sqlite3'

def find_in_history fb, arg
  arg.sub! /^\.\./, ''
  arg.gsub! /\s+/, '%'
  # prepend '%'
  arg = '%' + arg
  # Do not append '%' if arg ends with '$'
  arg += '%' if not arg.sub!(/\$$/, '')

  begin
    # Use SQLite3 gem instead of shell command for much better performance
    db_path = File.expand_path("~/Library/Application Support/Google/Chrome/Default/History.copy")
    return unless File.exist?(db_path)

    db = SQLite3::Database.new(db_path)
    db.results_as_hash = false  # Get arrays instead of hashes for better performance

    # Prepare the query with placeholders to avoid SQL injection
    sql = "SELECT DISTINCT title, url FROM urls WHERE title LIKE ? OR url LIKE ? ORDER BY last_visit_time DESC LIMIT 50"

    urls = fb.items.map {|item| item[:arg] }

    db.execute(sql, arg, arg) do |row|
      title = row[0]
      url = row[1]

      next if urls.include? url
      next if title.nil? || url.nil?

      # Escape XML characters
      title = title.gsub('&', '&amp;').gsub('<', '&lt;').gsub('>', '&gt;')
      url = url.gsub('&', '&amp;').gsub('<', '&lt;').gsub('>', '&gt;')

      fb.add_item(
        :title => ".. #{title}",
        :subtitle => url,
        :uid => url,
        :arg => url,
        :autocomplete => url,
        :icon => {:name => "/Applications/Google Chrome.app/Contents/Resources/document.icns"})
    end

  rescue SQLite3::Exception => e
    # Silently fail if database is locked or doesn't exist
    # STDERR.puts "SQLite error: #{e.message}" if ENV['DEBUG']
  ensure
    db.close if db
  end
end

#def history arg
#  s = '<?xml version="1.0"?><items>'

#  history = `sqlite3 -separator ":::" \
#    "$HOME/Library/Application Support/Google/Chrome/Default/History.copy" \
#    "SELECT distinct title,url FROM urls WHERE title like '%#{arg}%' OR url like '%#{arg}%' ORDER BY last_visit_time DESC LIMIT 50 COLLATE NOCASE"`

#  found = false
#  history.force_encoding(Encoding::UTF_8).split("\n").each do |line|
#    found = true
#    row   = line.gsub('<', '&lt;'.gsub('>', '&gt;')).gsub('&', '&amp;').split(':::')
#    title = row[0]
#    url   = row[1]
#    s += "<item>\n"
#    s += "  <arg>#{url}</arg>\n"
#    s += "  <title>#{title}</title>\n"
#    s += "  <subtitle>#{url}</subtitle>\n"
#    s += "  <icon>/Applications/Google Chrome.app/Contents/Resources/document.icns</icon>\n"
#    s += "</item>\n"
#  end

#  if found
#    s + "</items>"
#  end
#end

#if __FILE__ == $0
#  puts history ARGV[0]
#end

