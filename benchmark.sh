#!/bin/bash

echo "Performance Comparison: <PERSON> vs Nim"
echo "===================================="

# Test queries
queries=("test" "google" "1234" "github.com" "jira-123")

echo "Testing Ruby implementation (filter.rb):"
for query in "${queries[@]}"; do
    echo -n "Query '$query': "
    time_output=$(time (ruby filter.rb $query > /dev/null) 2>&1)
    echo "$time_output" | grep real | awk '{print $2}'
done

echo ""
echo "Testing Nim implementation (filter):"
for query in "${queries[@]}"; do
    echo -n "Query '$query': "
    time_output=$(time (./filter $query > /dev/null) 2>&1)
    echo "$time_output" | grep real | awk '{print $2}'
done

echo ""
echo "Running 100 iterations for more accurate timing:"
echo "Ruby (100 iterations):"
time_output=$(time (for i in {1..100}; do ruby filter.rb test > /dev/null; done) 2>&1)
echo "$time_output" | grep real | awk '{print "Total: " $2}'

echo "Nim (100 iterations):"
time_output=$(time (for i in {1..100}; do ./filter test > /dev/null; done) 2>&1)
echo "$time_output" | grep real | awk '{print "Total: " $2}'
