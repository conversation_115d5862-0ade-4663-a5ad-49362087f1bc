# Migration Guide: Ruby to Nim

This guide explains how to migrate from the Ruby implementations (`filter.rb` and `filter_tabs.rb`) to the new Nim implementations for improved performance.

## Quick Migration Steps

### 1. Build the Nim Executables

```bash
# Build optimized release versions
nim c -d:release filter.nim
nim c -d:release filter_tabs.nim
```

### 2. Update Alfred Workflow

#### For `filter.rb` replacement:
- Change script command from: `ruby filter.rb {query}`
- To: `./filter {query}`

#### For `filter_tabs.rb` replacement:
- Change script command from: `ruby filter_tabs.rb {query}`
- To: `./filter_tabs {query}`

### 3. Test Functionality

```bash
# Test basic search
./filter test
./filter_tabs test

# Test JIRA ticket detection
./filter 1234
./filter_tabs abc-123

# Test URL detection
./filter google.com
./filter_tabs github.com
```

## Performance Comparison

### Expected Improvements

| Metric | Ruby | Nim | Improvement |
|--------|------|-----|-------------|
| Startup Time | ~50ms | ~5ms | **10x faster** |
| Execution Time | ~100-200ms | ~20-50ms | **3-5x faster** |
| Memory Usage | ~15MB | ~3MB | **5x less** |
| Binary Size | N/A (interpreter) | ~2MB | Standalone |

### Benchmarking

Run the included benchmark script:

```bash
chmod +x benchmark.sh
./benchmark.sh
```

## Feature Compatibility

### ✅ Fully Compatible Features

- **Browser Tab Retrieval**: Identical AppleScript-based Chrome integration
- **JIRA Ticket Detection**: Same patterns (4-digit → ZEUS-XXXX, PROJECT-NUMBER)
- **URL Detection**: Same regex patterns for HTTP/HTTPS and domain detection
- **XML Output**: Identical Alfred workflow XML format
- **History Search**: Same SQLite query approach
- **Icon Handling**: Same browser icon paths

### 🔄 Behavioral Differences

#### `filter.nim` vs `filter.rb`
- **Matching Algorithm**: Slightly optimized regex compilation
- **Error Handling**: More robust exception handling
- **Performance**: Significantly faster execution

#### `filter_tabs.nim` vs `filter_tabs.rb`
- **Fallback Item**: Always includes Google search item (like Ruby version)
- **History Threshold**: Searches history when < 5 items (vs Ruby's variable threshold)
- **Matching**: Simplified regex for better performance

### ⚠️ Minor Differences

1. **Error Messages**: Nim versions fail silently like Ruby but with different internal error handling
2. **Regex Engine**: Uses PCRE (same as Ruby) but with Nim's regex implementation
3. **String Encoding**: UTF-8 handling may differ slightly in edge cases

## Troubleshooting

### Common Issues

#### 1. Permission Denied
```bash
chmod +x filter filter_tabs
```

#### 2. Chrome Not Found
- Ensure Google Chrome is running
- Check AppleScript permissions in System Preferences

#### 3. History Database Access
- Verify Chrome history file exists: `~/Library/Application Support/Google/Chrome/Default/History.copy`
- Check SQLite3 is available: `which sqlite3`

#### 4. Compilation Errors
```bash
# Install Nim if not available
curl https://nim-lang.org/choosenim/init.sh -sSf | sh

# Update Nim
choosenim update stable
```

### Performance Issues

If performance is not as expected:

1. **Ensure Release Build**:
   ```bash
   nim c -d:release filter.nim
   ```

2. **Check System Load**:
   ```bash
   top -l 1 | grep "CPU usage"
   ```

3. **Profile Execution**:
   ```bash
   time ./filter test
   ```

## Rollback Plan

If you need to rollback to Ruby:

1. **Keep Ruby Files**: Don't delete `filter.rb` and `filter_tabs.rb`
2. **Revert Alfred Workflow**: Change script commands back to Ruby
3. **Test Functionality**: Ensure Ruby versions still work

## Advanced Configuration

### Custom Compilation Flags

```bash
# Maximum optimization
nim c -d:release -d:lto --opt:speed filter.nim

# Debug build with symbols
nim c -d:debug --debugger:native filter.nim

# Cross-compilation (if needed)
nim c -d:release --cpu:arm64 filter.nim
```

### Library Customization

The shared library in `lib/` can be customized:

- **`lib/browser_tabs.nim`**: Add support for other browsers
- **`lib/matching.nim`**: Modify matching algorithms
- **`lib/utils.nim`**: Add custom JIRA patterns
- **`lib/history.nim`**: Change history search logic

## Validation Checklist

Before completing migration:

- [ ] Both executables compile without errors
- [ ] Basic search returns expected results
- [ ] JIRA ticket detection works (test with `1234` and `abc-123`)
- [ ] URL detection works (test with `google.com`)
- [ ] History search returns results (test with common terms)
- [ ] Alfred workflow integration functions correctly
- [ ] Performance improvement is noticeable
- [ ] No regression in functionality

## Support

If you encounter issues:

1. Check this migration guide
2. Review the main README_NIM.md
3. Test with the benchmark script
4. Compare output with Ruby versions using `diff`

The Nim implementations are designed to be drop-in replacements with improved performance while maintaining full compatibility with existing Alfred workflows.
