## Browser tabs filtering - Nim implementation of filter_tabs.rb
## This is a simpler version that focuses only on tab filtering without history search

import std/[os, strutils]
import lib/[types, browser_tabs, matching, utils, xml_output, history]

proc main() =
  let args = commandLineParams()
  let query = args.join(" ").strip().toLower()
  let tabs = getBrowserTabs()
  var fb = newFeedback()
  
  # Filter tabs using simple regex matching (like the Ruby version)
  for tab in tabs:
    if tabMatchesQuerySimple(tab, query):
      fb.addItem(
        tab.title,
        tab.url,
        tab.url,
        tab.url,
        tab.url,
        iconForTab(tab)
      )
  
  # Add JIRA ticket if applicable
  addJiraTicketIfNeeded(fb, query)
  
  # Add fallback search item
  let title = args.join(" ").strip()
  var arg: string
  if isUrl(title):
    arg = title
    if not arg.startsWith("http"):
      arg = "http://" & arg
  else:
    arg = "https://www.google.com/search?q=" & title.replace(" ", "+")
  
  fb.addItem(
    title,
    arg,
    arg,
    arg,
    title,
    "/Applications/Google Chrome.app/Contents/Resources/document.icns"
  )
  
  # Add history if we have less than 5 items
  if fb.items.len < 5:
    findInHistory(fb, query)
  
  # Output XML
  echo toXmlWithVariables(fb, title)

when isMainModule:
  main()
